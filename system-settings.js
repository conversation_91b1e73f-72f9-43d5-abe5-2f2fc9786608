// System Settings Manager
class SystemSettingsManager {
    constructor() {
        this.settings = {
            notifications: {
                enabled: true,
                soundEnabled: true,
                desktopEnabled: false,
                checkInterval: 60000
            },
            backup: {
                autoBackup: true,
                compressionEnabled: true,
                maxBackups: 30
            },
            data: {
                autoCleanup: false
            },
            security: {
                auditLog: true,
                sessionTimeout: 0
            }
        };
        this.sessionStartTime = Date.now();
        this.init();
    }

    init() {
        this.loadSettings();
        this.updateUI();
        this.loadSystemInfo();
        this.startSessionTimer();
    }

    loadSettings() {
        const saved = localStorage.getItem('systemSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    saveSettings() {
        localStorage.setItem('systemSettings', JSON.stringify(this.settings));
        this.applySettings();
    }

    applySettings() {
        // Apply notification settings
        if (window.notificationSystem) {
            window.notificationSystem.updateSettings(this.settings.notifications);
        }

        // Apply backup settings
        if (window.backupSystem) {
            window.backupSystem.maxBackups = this.settings.backup.maxBackups;
            window.backupSystem.compressionEnabled = this.settings.backup.compressionEnabled;
        }
    }

    updateUI() {
        // Update toggle switches
        this.updateToggle('notificationsEnabled', this.settings.notifications.enabled);
        this.updateToggle('soundEnabled', this.settings.notifications.soundEnabled);
        this.updateToggle('desktopEnabled', this.settings.notifications.desktopEnabled);
        this.updateToggle('autoBackup', this.settings.backup.autoBackup);
        this.updateToggle('compressionEnabled', this.settings.backup.compressionEnabled);
        this.updateToggle('autoCleanup', this.settings.data.autoCleanup);
        this.updateToggle('auditLog', this.settings.security.auditLog);

        // Update select elements
        document.getElementById('checkInterval').value = this.settings.notifications.checkInterval;
        document.getElementById('maxBackups').value = this.settings.backup.maxBackups;
        document.getElementById('sessionTimeout').value = this.settings.security.sessionTimeout;
    }

    updateToggle(elementId, isActive) {
        const element = document.getElementById(elementId);
        if (element) {
            if (isActive) {
                element.classList.add('active');
            } else {
                element.classList.remove('active');
            }
        }
    }

    loadSystemInfo() {
        // Calculate total records
        const dataTypes = ['employees', 'sickLeave', 'annualLeave', 'penalties', 'absences', 'training', 'vacations', 'evaluations'];
        let totalRecords = 0;
        
        dataTypes.forEach(type => {
            totalRecords += dataManager.getAll(type).length;
        });

        document.getElementById('totalRecords').textContent = totalRecords;

        // Calculate storage used
        let storageUsed = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                storageUsed += localStorage[key].length;
            }
        }
        
        const storageKB = Math.round(storageUsed / 1024);
        document.getElementById('storageUsed').textContent = `${storageKB} KB`;

        // Last backup
        const lastBackup = localStorage.getItem('lastBackupTime');
        if (lastBackup) {
            const backupDate = new Date(parseInt(lastBackup));
            document.getElementById('lastBackup').textContent = backupDate.toLocaleDateString('ar-SA');
        }
    }

    startSessionTimer() {
        setInterval(() => {
            const uptime = Math.floor((Date.now() - this.sessionStartTime) / 60000);
            document.getElementById('systemUptime').textContent = `${uptime} دقيقة`;
        }, 60000);
    }

    toggleSetting(settingId) {
        const element = document.getElementById(settingId);
        const isActive = element.classList.contains('active');
        
        if (isActive) {
            element.classList.remove('active');
        } else {
            element.classList.add('active');
        }

        // Update settings object
        switch (settingId) {
            case 'notificationsEnabled':
                this.settings.notifications.enabled = !isActive;
                break;
            case 'soundEnabled':
                this.settings.notifications.soundEnabled = !isActive;
                break;
            case 'desktopEnabled':
                this.settings.notifications.desktopEnabled = !isActive;
                break;
            case 'autoBackup':
                this.settings.backup.autoBackup = !isActive;
                break;
            case 'compressionEnabled':
                this.settings.backup.compressionEnabled = !isActive;
                break;
            case 'autoCleanup':
                this.settings.data.autoCleanup = !isActive;
                break;
            case 'auditLog':
                this.settings.security.auditLog = !isActive;
                break;
        }

        this.saveSettings();
    }

    updateCheckInterval() {
        const value = document.getElementById('checkInterval').value;
        this.settings.notifications.checkInterval = parseInt(value);
        this.saveSettings();
    }

    updateMaxBackups() {
        const value = document.getElementById('maxBackups').value;
        this.settings.backup.maxBackups = parseInt(value);
        this.saveSettings();
    }

    updateSessionTimeout() {
        const value = document.getElementById('sessionTimeout').value;
        this.settings.security.sessionTimeout = parseInt(value);
        this.saveSettings();
        
        if (value > 0) {
            this.setupSessionTimeout(value);
        }
    }

    setupSessionTimeout(timeout) {
        let lastActivity = Date.now();
        
        const resetTimer = () => {
            lastActivity = Date.now();
        };

        // Track user activity
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });

        // Check for timeout
        setInterval(() => {
            if (Date.now() - lastActivity > timeout) {
                alert('انتهت مدة الجلسة. سيتم تسجيل الخروج.');
                localStorage.removeItem('isLoggedIn');
                window.location.href = 'index.html';
            }
        }, 60000); // Check every minute
    }

    createManualBackup() {
        const description = prompt('أدخل وصف للنسخة الاحتياطية (اختياري):');
        
        try {
            const backupId = window.backupSystem.createManualBackup(description || '');
            alert(`تم إنشاء النسخة الاحتياطية بنجاح: ${backupId}`);
            this.loadSystemInfo();
        } catch (error) {
            alert('فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        }
    }

    showBackupManager() {
        const backupList = window.backupSystem.getBackupList();
        const container = document.getElementById('backupList');
        
        if (backupList.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">لا توجد نسخ احتياطية</p>';
        } else {
            const backupsHTML = backupList.map(backup => `
                <div class="backup-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>النسخة: ${backup.id}</strong><br>
                            <small class="text-muted">
                                التاريخ: ${new Date(backup.timestamp).toLocaleString('ar-SA')}<br>
                                الحجم: ${Math.round(backup.size / 1024)} KB
                            </small>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="restoreBackup('${backup.id}')">
                                <i class="bi bi-arrow-clockwise"></i>
                                استعادة
                            </button>
                            <button class="btn btn-outline-success" onclick="exportBackup('${backup.id}')">
                                <i class="bi bi-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteBackup('${backup.id}')">
                                <i class="bi bi-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = backupsHTML;
        }

        const modal = new bootstrap.Modal(document.getElementById('backupManagerModal'));
        modal.show();
    }

    optimizeDatabase() {
        if (confirm('هل أنت متأكد من تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
            try {
                // Remove duplicates and optimize data
                const dataTypes = ['employees', 'sickLeave', 'annualLeave', 'penalties', 'absences', 'training', 'vacations', 'evaluations'];
                let optimizedCount = 0;

                dataTypes.forEach(type => {
                    const data = dataManager.getAll(type);
                    const uniqueData = data.filter((item, index, self) => 
                        index === self.findIndex(t => t.id === item.id)
                    );
                    
                    if (uniqueData.length !== data.length) {
                        localStorage.setItem(type, JSON.stringify(uniqueData));
                        optimizedCount += data.length - uniqueData.length;
                    }
                });

                alert(`تم تحسين قاعدة البيانات بنجاح. تم حذف ${optimizedCount} سجل مكرر.`);
                this.loadSystemInfo();
            } catch (error) {
                alert('فشل في تحسين قاعدة البيانات: ' + error.message);
            }
        }
    }

    exportAllData() {
        try {
            const allData = {
                employees: dataManager.getAll('employees'),
                sickLeave: dataManager.getAll('sickLeave'),
                annualLeave: dataManager.getAll('annualLeave'),
                penalties: dataManager.getAll('penalties'),
                absences: dataManager.getAll('absences'),
                training: dataManager.getAll('training'),
                vacations: dataManager.getAll('vacations'),
                evaluations: dataManager.getAll('evaluations'),
                settings: this.settings,
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `complete_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('تم تصدير جميع البيانات بنجاح');
        } catch (error) {
            alert('فشل في تصدير البيانات: ' + error.message);
        }
    }

    resetSystem() {
        const confirmation = prompt('لإعادة تعيين النظام، اكتب "RESET" بالأحرف الكبيرة:');
        
        if (confirmation === 'RESET') {
            if (confirm('هذا الإجراء سيحذف جميع البيانات نهائياً. هل أنت متأكد؟')) {
                try {
                    // Clear all data
                    const dataTypes = ['employees', 'sickLeave', 'annualLeave', 'penalties', 'absences', 'training', 'vacations', 'evaluations'];
                    dataTypes.forEach(type => {
                        localStorage.removeItem(type);
                    });

                    // Clear settings and other data
                    localStorage.removeItem('systemSettings');
                    localStorage.removeItem('notifications');
                    localStorage.removeItem('searchHistory');
                    localStorage.removeItem('backupList');
                    localStorage.removeItem('lastBackupTime');

                    alert('تم إعادة تعيين النظام بنجاح. سيتم إعادة تحميل الصفحة.');
                    location.reload();
                } catch (error) {
                    alert('فشل في إعادة تعيين النظام: ' + error.message);
                }
            }
        } else {
            alert('تم إلغاء العملية');
        }
    }

    viewAuditLog() {
        // This would show audit log if implemented
        alert('سيتم إضافة ميزة سجل العمليات قريباً');
    }

    changePassword() {
        const currentPassword = prompt('أدخل كلمة المرور الحالية:');
        if (currentPassword === '2026') {
            const newPassword = prompt('أدخل كلمة المرور الجديدة:');
            if (newPassword && newPassword.length >= 4) {
                const confirmPassword = prompt('أكد كلمة المرور الجديدة:');
                if (newPassword === confirmPassword) {
                    localStorage.setItem('systemPassword', newPassword);
                    alert('تم تغيير كلمة المرور بنجاح');
                } else {
                    alert('كلمات المرور غير متطابقة');
                }
            } else {
                alert('كلمة المرور يجب أن تكون 4 أحرف على الأقل');
            }
        } else {
            alert('كلمة المرور الحالية غير صحيحة');
        }
    }

    cleanOldBackups() {
        if (confirm('هل أنت متأكد من حذف النسخ الاحتياطية القديمة؟')) {
            window.backupSystem.cleanOldBackups();
            alert('تم حذف النسخ القديمة بنجاح');
            this.showBackupManager();
        }
    }
}

// Global functions
function initializeSettings() {
    window.systemSettingsManager = new SystemSettingsManager();
}

function toggleSetting(settingId) {
    window.systemSettingsManager.toggleSetting(settingId);
}

function updateCheckInterval() {
    window.systemSettingsManager.updateCheckInterval();
}

function updateMaxBackups() {
    window.systemSettingsManager.updateMaxBackups();
}

function updateSessionTimeout() {
    window.systemSettingsManager.updateSessionTimeout();
}

function createManualBackup() {
    window.systemSettingsManager.createManualBackup();
}

function showBackupManager() {
    window.systemSettingsManager.showBackupManager();
}

function optimizeDatabase() {
    window.systemSettingsManager.optimizeDatabase();
}

function exportAllData() {
    window.systemSettingsManager.exportAllData();
}

function resetSystem() {
    window.systemSettingsManager.resetSystem();
}

function viewAuditLog() {
    window.systemSettingsManager.viewAuditLog();
}

function changePassword() {
    window.systemSettingsManager.changePassword();
}

function cleanOldBackups() {
    window.systemSettingsManager.cleanOldBackups();
}

function restoreBackup(backupId) {
    if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        const success = window.backupSystem.restoreFromBackup(backupId);
        if (success) {
            alert('تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل الصفحة.');
            location.reload();
        }
    }
}

function exportBackup(backupId) {
    try {
        window.backupSystem.exportBackup(backupId);
        alert('تم تصدير النسخة الاحتياطية بنجاح');
    } catch (error) {
        alert('فشل في تصدير النسخة الاحتياطية: ' + error.message);
    }
}

function deleteBackup(backupId) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        window.backupSystem.deleteBackup(backupId);
        alert('تم حذف النسخة الاحتياطية بنجاح');
        window.systemSettingsManager.showBackupManager();
    }
}
