// PDF Reports Generator
class PDFReportsGenerator {
    constructor() {
        this.loadJsPDF();
    }

    // Load jsPDF library dynamically
    async loadJsPDF() {
        if (typeof window.jsPDF === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script);
            
            return new Promise((resolve) => {
                script.onload = () => {
                    this.jsPDF = window.jspdf.jsPDF;
                    resolve();
                };
            });
        } else {
            this.jsPDF = window.jspdf.jsPDF;
        }
    }

    // Generate comprehensive employee report
    async generateEmployeeReport(employeeId = null) {
        await this.loadJsPDF();
        
        const doc = new this.jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Set Arabic font support
        this.setupArabicFont(doc);

        const employees = employeeId ? 
            [dataManager.getById('employees', employeeId)] : 
            dataManager.getAll('employees');

        if (!employees || employees.length === 0) {
            throw new Error('لا توجد بيانات موظفين');
        }

        let yPosition = 20;

        // Header
        yPosition = this.addReportHeader(doc, 'تقرير بيانات الموظفين', yPosition);

        employees.forEach((employee, index) => {
            if (index > 0) {
                doc.addPage();
                yPosition = 20;
            }

            yPosition = this.addEmployeeDetails(doc, employee, yPosition);
            yPosition = this.addEmployeeHistory(doc, employee, yPosition);
        });

        // Footer
        this.addReportFooter(doc);

        // Save PDF
        const fileName = employeeId ? 
            `employee_report_${employeeId}_${new Date().toISOString().split('T')[0]}.pdf` :
            `all_employees_report_${new Date().toISOString().split('T')[0]}.pdf`;

        doc.save(fileName);
    }

    // Generate leave report
    async generateLeaveReport(type = 'all', dateFrom = null, dateTo = null) {
        await this.loadJsPDF();
        
        const doc = new this.jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
        });

        this.setupArabicFont(doc);

        let data = [];
        let title = '';

        switch (type) {
            case 'sick':
                data = dataManager.getAll('sickLeave');
                title = 'تقرير العطل المرضية';
                break;
            case 'annual':
                data = dataManager.getAll('annualLeave');
                title = 'تقرير العطل السنوية';
                break;
            case 'vacation':
                data = dataManager.getAll('vacations');
                title = 'تقرير الإجازات';
                break;
            default:
                data = [
                    ...dataManager.getAll('sickLeave'),
                    ...dataManager.getAll('annualLeave'),
                    ...dataManager.getAll('vacations')
                ];
                title = 'تقرير شامل للعطل والإجازات';
        }

        // Filter by date range
        if (dateFrom && dateTo) {
            const from = new Date(dateFrom);
            const to = new Date(dateTo);
            data = data.filter(item => {
                if (item.startDate) {
                    const itemDate = new Date(item.startDate);
                    return itemDate >= from && itemDate <= to;
                }
                return false;
            });
            title += ` (من ${dateFrom} إلى ${dateTo})`;
        }

        let yPosition = 20;
        yPosition = this.addReportHeader(doc, title, yPosition);
        yPosition = this.addLeaveTable(doc, data, yPosition);

        this.addReportFooter(doc);

        const fileName = `leave_report_${type}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
    }

    // Generate statistics report
    async generateStatisticsReport() {
        await this.loadJsPDF();
        
        const doc = new this.jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        this.setupArabicFont(doc);

        let yPosition = 20;
        yPosition = this.addReportHeader(doc, 'تقرير الإحصائيات الشامل', yPosition);

        // General statistics
        const stats = dataManager.getStatistics();
        yPosition = this.addStatisticsSection(doc, 'الإحصائيات العامة', stats, yPosition);

        // Employee statistics by rank
        const rankStats = statisticsManager.getEmployeesByRank();
        yPosition = this.addStatisticsSection(doc, 'توزيع الموظفين حسب الرتبة', rankStats, yPosition);

        // Employee statistics by battalion
        const battalionStats = statisticsManager.getEmployeesByBattalion();
        yPosition = this.addStatisticsSection(doc, 'توزيع الموظفين حسب الكتيبة', battalionStats, yPosition);

        this.addReportFooter(doc);

        const fileName = `statistics_report_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
    }

    // Generate penalty report
    async generatePenaltyReport(dateFrom = null, dateTo = null) {
        await this.loadJsPDF();
        
        const doc = new this.jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
        });

        this.setupArabicFont(doc);

        let penalties = dataManager.getAll('penalties');

        // Filter by date range
        if (dateFrom && dateTo) {
            const from = new Date(dateFrom);
            const to = new Date(dateTo);
            penalties = penalties.filter(penalty => {
                if (penalty.incidentDate) {
                    const incidentDate = new Date(penalty.incidentDate);
                    return incidentDate >= from && incidentDate <= to;
                }
                return false;
            });
        }

        let yPosition = 20;
        const title = dateFrom && dateTo ? 
            `تقرير العقوبات (من ${dateFrom} إلى ${dateTo})` : 
            'تقرير العقوبات الشامل';

        yPosition = this.addReportHeader(doc, title, yPosition);
        yPosition = this.addPenaltyTable(doc, penalties, yPosition);

        this.addReportFooter(doc);

        const fileName = `penalty_report_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
    }

    // Setup Arabic font (simplified)
    setupArabicFont(doc) {
        // Note: For full Arabic support, you would need to add Arabic fonts
        // This is a simplified version using default fonts
        doc.setFont('helvetica');
        doc.setFontSize(12);
    }

    // Add report header
    addReportHeader(doc, title, yPosition) {
        // Logo area (placeholder)
        doc.setFillColor(108, 92, 231);
        doc.rect(20, yPosition, 170, 25, 'F');
        
        // Title
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(18);
        doc.text(title, 105, yPosition + 15, { align: 'center' });
        
        // Date and time
        doc.setFontSize(10);
        const now = new Date();
        const dateStr = now.toLocaleDateString('ar-SA');
        const timeStr = now.toLocaleTimeString('ar-SA');
        doc.text(`تاريخ التقرير: ${dateStr} - ${timeStr}`, 190, yPosition + 20, { align: 'right' });
        
        doc.setTextColor(0, 0, 0);
        return yPosition + 35;
    }

    // Add employee details
    addEmployeeDetails(doc, employee, yPosition) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(`بيانات الموظف: ${employee.firstName} ${employee.lastName}`, 20, yPosition);
        
        yPosition += 10;
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');

        const details = [
            ['الرتبة:', employee.rank || 'غير محدد'],
            ['رقم الذاتية:', employee.personalNumber || 'غير محدد'],
            ['مكان العمل:', employee.workplace || 'غير محدد'],
            ['الكتيبة:', employee.battalion || 'غير محدد'],
            ['تاريخ الميلاد:', employee.birthDate || 'غير محدد'],
            ['السن:', employee.age ? `${employee.age} سنة` : 'غير محدد'],
            ['الحالة العائلية:', employee.maritalStatus || 'غير محدد'],
            ['عدد الأولاد:', employee.children || '0'],
            ['رقم الهاتف:', employee.phone || 'غير محدد'],
            ['تاريخ التنصيب:', employee.appointmentDate || 'غير محدد']
        ];

        details.forEach(([label, value], index) => {
            const x = index % 2 === 0 ? 20 : 110;
            const y = yPosition + Math.floor(index / 2) * 7;
            doc.text(`${label} ${value}`, x, y);
        });

        return yPosition + Math.ceil(details.length / 2) * 7 + 10;
    }

    // Add employee history
    addEmployeeHistory(doc, employee, yPosition) {
        // Check if we need a new page
        if (yPosition > 250) {
            doc.addPage();
            yPosition = 20;
        }

        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('سجل الموظف:', 20, yPosition);
        yPosition += 10;

        // Get employee's records
        const sickLeaves = dataManager.getByEmployeeId ? 
            dataManager.getByEmployeeId('sickLeave', employee.id) : 
            dataManager.getAll('sickLeave').filter(leave => leave.personalNumber === employee.personalNumber);

        const penalties = dataManager.getByEmployeeId ? 
            dataManager.getByEmployeeId('penalties', employee.id) : 
            dataManager.getAll('penalties').filter(penalty => penalty.personalNumber === employee.personalNumber);

        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');

        if (sickLeaves.length > 0) {
            doc.text(`العطل المرضية: ${sickLeaves.length}`, 20, yPosition);
            yPosition += 7;
        }

        if (penalties.length > 0) {
            doc.text(`العقوبات: ${penalties.length}`, 20, yPosition);
            yPosition += 7;
        }

        return yPosition + 10;
    }

    // Add leave table
    addLeaveTable(doc, data, yPosition) {
        if (data.length === 0) {
            doc.text('لا توجد بيانات', 20, yPosition);
            return yPosition + 10;
        }

        // Table headers
        const headers = ['الاسم', 'الرتبة', 'رقم الذاتية', 'نوع العطلة', 'تاريخ البداية', 'المدة', 'الأيام المتبقية'];
        const colWidths = [30, 25, 25, 35, 30, 20, 25];
        let xPosition = 20;

        // Draw headers
        doc.setFillColor(240, 240, 240);
        doc.rect(20, yPosition, 190, 8, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(9);

        headers.forEach((header, index) => {
            doc.text(header, xPosition + 2, yPosition + 6);
            xPosition += colWidths[index];
        });

        yPosition += 8;
        doc.setFont('helvetica', 'normal');

        // Draw data rows
        data.slice(0, 20).forEach((item, rowIndex) => { // Limit to 20 rows per page
            xPosition = 20;
            const rowY = yPosition + (rowIndex * 8);

            // Alternate row colors
            if (rowIndex % 2 === 0) {
                doc.setFillColor(250, 250, 250);
                doc.rect(20, rowY, 190, 8, 'F');
            }

            const rowData = [
                `${item.firstName || ''} ${item.lastName || ''}`,
                item.rank || '',
                item.personalNumber || '',
                item.leaveType || item.trainingType || 'إجازة',
                item.startDate || '',
                item.duration ? `${item.duration} يوم` : '',
                item.startDate && item.duration ? 
                    `${dataManager.calculateRemainingDays(item.startDate, item.duration)} يوم` : ''
            ];

            rowData.forEach((cellData, colIndex) => {
                doc.text(cellData.substring(0, 15), xPosition + 2, rowY + 6); // Truncate long text
                xPosition += colWidths[colIndex];
            });
        });

        return yPosition + (Math.min(data.length, 20) * 8) + 10;
    }

    // Add penalty table
    addPenaltyTable(doc, penalties, yPosition) {
        if (penalties.length === 0) {
            doc.text('لا توجد عقوبات', 20, yPosition);
            return yPosition + 10;
        }

        // Table headers
        const headers = ['الاسم', 'الرتبة', 'نوع العقوبة', 'الدرجة', 'تاريخ الخطأ', 'رقم المقرر'];
        const colWidths = [35, 25, 40, 20, 30, 30];
        let xPosition = 20;

        // Draw headers
        doc.setFillColor(240, 240, 240);
        doc.rect(20, yPosition, 180, 8, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(9);

        headers.forEach((header, index) => {
            doc.text(header, xPosition + 2, yPosition + 6);
            xPosition += colWidths[index];
        });

        yPosition += 8;
        doc.setFont('helvetica', 'normal');

        // Draw data rows
        penalties.slice(0, 20).forEach((penalty, rowIndex) => {
            xPosition = 20;
            const rowY = yPosition + (rowIndex * 8);

            if (rowIndex % 2 === 0) {
                doc.setFillColor(250, 250, 250);
                doc.rect(20, rowY, 180, 8, 'F');
            }

            const rowData = [
                `${penalty.firstName || ''} ${penalty.lastName || ''}`,
                penalty.rank || '',
                penalty.penaltyType || '',
                penalty.degree || '',
                penalty.incidentDate || '',
                penalty.decisionNumber || ''
            ];

            rowData.forEach((cellData, colIndex) => {
                doc.text(cellData.substring(0, 12), xPosition + 2, rowY + 6);
                xPosition += colWidths[colIndex];
            });
        });

        return yPosition + (Math.min(penalties.length, 20) * 8) + 10;
    }

    // Add statistics section
    addStatisticsSection(doc, title, stats, yPosition) {
        // Check if we need a new page
        if (yPosition > 250) {
            doc.addPage();
            yPosition = 20;
        }

        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(title, 20, yPosition);
        yPosition += 10;

        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');

        Object.keys(stats).forEach((key, index) => {
            const value = stats[key];
            doc.text(`${key}: ${value}`, 20, yPosition + (index * 7));
        });

        return yPosition + (Object.keys(stats).length * 7) + 15;
    }

    // Add report footer
    addReportFooter(doc) {
        const pageCount = doc.internal.getNumberOfPages();
        
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setTextColor(128, 128, 128);
            doc.text(`صفحة ${i} من ${pageCount}`, 105, 290, { align: 'center' });
            doc.text('نظام إدارة بيانات الموظفين', 20, 290);
            doc.text(new Date().toLocaleDateString('ar-SA'), 190, 290, { align: 'right' });
        }
    }

    // Generate custom report
    async generateCustomReport(title, data, columns) {
        await this.loadJsPDF();
        
        const doc = new this.jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
        });

        this.setupArabicFont(doc);

        let yPosition = 20;
        yPosition = this.addReportHeader(doc, title, yPosition);
        yPosition = this.addCustomTable(doc, data, columns, yPosition);

        this.addReportFooter(doc);

        const fileName = `custom_report_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
    }

    // Add custom table
    addCustomTable(doc, data, columns, yPosition) {
        if (data.length === 0) {
            doc.text('لا توجد بيانات', 20, yPosition);
            return yPosition + 10;
        }

        const colWidth = 270 / columns.length;
        let xPosition = 20;

        // Draw headers
        doc.setFillColor(240, 240, 240);
        doc.rect(20, yPosition, 270, 8, 'F');
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(9);

        columns.forEach((column, index) => {
            doc.text(column, xPosition + 2, yPosition + 6);
            xPosition += colWidth;
        });

        yPosition += 8;
        doc.setFont('helvetica', 'normal');

        // Draw data rows
        data.slice(0, 25).forEach((item, rowIndex) => {
            xPosition = 20;
            const rowY = yPosition + (rowIndex * 8);

            if (rowIndex % 2 === 0) {
                doc.setFillColor(250, 250, 250);
                doc.rect(20, rowY, 270, 8, 'F');
            }

            columns.forEach((column, colIndex) => {
                const cellData = item[column] || '';
                doc.text(cellData.toString().substring(0, 15), xPosition + 2, rowY + 6);
                xPosition += colWidth;
            });
        });

        return yPosition + (Math.min(data.length, 25) * 8) + 10;
    }
}

// Initialize PDF reports generator
const pdfReportsGenerator = new PDFReportsGenerator();

// Export for global use
window.pdfReportsGenerator = pdfReportsGenerator;
