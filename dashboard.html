<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم التنفيذية - نظام إدارة بيانات الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .metric-change {
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .alert-item {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .alert-urgent { border-left-color: #e74c3c; }
        .alert-warning { border-left-color: #f39c12; }
        .alert-info { border-left-color: #3498db; }
        
        .quick-action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            color: white;
            padding: 15px 20px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            color: white;
        }
        
        .performance-indicator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .performance-bar {
            width: 100px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="main-body">
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="bi bi-speedometer2"></i>
                <h1>لوحة التحكم التنفيذية</h1>
            </div>
            
            <div class="datetime">
                <div class="date" id="currentDate"></div>
                <div class="time" id="currentTime"></div>
            </div>
            
            <div class="user-info">
                <a href="main.html" class="btn btn-primary">
                    <i class="bi bi-house-fill"></i>
                    الرئيسية
                </a>
                <button class="logout-btn" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <main class="dashboard">
        <!-- Key Metrics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="totalEmployees">0</div>
                    <div class="metric-label">إجمالي الموظفين</div>
                    <div class="metric-change">
                        <i class="bi bi-arrow-up"></i>
                        <span id="employeeChange">+0%</span> من الشهر الماضي
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="activeLeaves">0</div>
                    <div class="metric-label">العطل النشطة</div>
                    <div class="metric-change">
                        <i class="bi bi-calendar-check"></i>
                        <span id="leaveChange">0</span> عطلة جديدة هذا الأسبوع
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="pendingActions">0</div>
                    <div class="metric-label">الإجراءات المعلقة</div>
                    <div class="metric-change">
                        <i class="bi bi-exclamation-triangle"></i>
                        <span id="actionChange">0</span> تحتاج متابعة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="attendanceRate">0%</div>
                    <div class="metric-label">معدل الحضور</div>
                    <div class="metric-change">
                        <i class="bi bi-graph-up"></i>
                        <span id="attendanceChange">+0%</span> من الأسبوع الماضي
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="chart-container">
                    <h5><i class="bi bi-bar-chart-fill"></i> توزيع الموظفين حسب الكتائب</h5>
                    <canvas id="battalionChart" width="400" height="200"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h5><i class="bi bi-pie-chart-fill"></i> توزيع العطل</h5>
                    <canvas id="leaveChart" width="200" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Alerts and Quick Actions Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="chart-container">
                    <h5><i class="bi bi-bell-fill"></i> التنبيهات والإشعارات</h5>
                    <div id="alertsContainer">
                        <!-- Alerts will be populated here -->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h5><i class="bi bi-lightning-fill"></i> الإجراءات السريعة</h5>
                    <div class="row">
                        <div class="col-6">
                            <button class="quick-action-btn w-100" onclick="quickAddEmployee()">
                                <i class="bi bi-person-plus"></i><br>
                                إضافة موظف
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="quick-action-btn w-100" onclick="quickBackup()">
                                <i class="bi bi-cloud-download"></i><br>
                                نسخ احتياطي
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="quick-action-btn w-100" onclick="generateReport()">
                                <i class="bi bi-file-earmark-pdf"></i><br>
                                تقرير PDF
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="quick-action-btn w-100" onclick="bulkImport()">
                                <i class="bi bi-upload"></i><br>
                                استيراد مجمع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Indicators -->
        <div class="row">
            <div class="col-md-12">
                <div class="chart-container">
                    <h5><i class="bi bi-graph-up-arrow"></i> مؤشرات الأداء</h5>
                    <div id="performanceIndicators">
                        <!-- Performance indicators will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="data-manager.js"></script>
    <script src="statistics.js"></script>
    <script src="dashboard-advanced.js"></script>

    <script>
        // Check authentication
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'index.html';
        }

        // Update date and time
        function updateDateTime() {
            const now = new Date();
            const dateOptions = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                locale: 'fr-FR'
            };
            
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
            
            document.getElementById('currentDate').textContent = 
                now.toLocaleDateString('fr-FR', dateOptions);
            document.getElementById('currentTime').textContent = 
                now.toLocaleTimeString('fr-FR', timeOptions);
        }

        setInterval(updateDateTime, 1000);
        updateDateTime();

        function logout() {
            if (confirm('هل أنت متأكد من الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                window.location.href = 'index.html';
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
