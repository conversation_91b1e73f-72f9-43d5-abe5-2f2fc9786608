// Demo Data for Employee Management System
// This file contains sample data for testing the system

function loadDemoData() {
    // Sample employees
    const sampleEmployees = [
        {
            firstName: "أحمد",
            lastName: "محمد",
            rank: "عميد ش",
            personalNumber: "12345",
            workplace: "المديرية العامة",
            battalion: "هياكل الدعم الإداري",
            maritalStatus: "متزوج (ة)",
            children: 2,
            birthDate: "1980-05-15",
            birthPlace: "الجزائر",
            bloodType: "A+",
            address: "حي السلام، الجزائر العاصمة",
            phone: "0555123456",
            individualNumber: "198005151234",
            appointmentDate: "2005-01-15"
        },
        {
            firstName: "فاطمة",
            lastName: "علي",
            rank: "محا ش",
            personalNumber: "12346",
            workplace: "المديرية العامة",
            battalion: "كتيبة الحماية و الأمن",
            maritalStatus: "متزوج (ة)",
            children: 1,
            birthDate: "1985-03-20",
            birthPlace: "وهران",
            bloodType: "B+",
            address: "حي النصر، وهران",
            phone: "0555123457",
            individualNumber: "198503201234",
            appointmentDate: "2010-09-01"
        },
        {
            firstName: "محمد",
            lastName: "بن علي",
            rank: "ض ش ر",
            personalNumber: "12347",
            workplace: "المديرية العامة",
            battalion: "كتيبة حركة المرور و أمن الطرقات",
            maritalStatus: "أعزب (ة)",
            children: 0,
            birthDate: "1990-12-10",
            birthPlace: "قسنطينة",
            bloodType: "O+",
            address: "حي بوذراع، قسنطينة",
            phone: "0555123458",
            individualNumber: "199012101234",
            appointmentDate: "2015-03-15"
        }
    ];

    // Sample sick leaves
    const sampleSickLeaves = [
        {
            firstName: "أحمد",
            lastName: "محمد",
            rank: "عميد ش",
            personalNumber: "12345",
            workplace: "المديرية العامة",
            battalion: "هياكل الدعم الإداري",
            leaveType: "عطلة مرضية عادية",
            duration: 7,
            startDate: "2024-01-15",
            endDate: "2024-01-21"
        },
        {
            firstName: "فاطمة",
            lastName: "علي",
            rank: "محا ش",
            personalNumber: "12346",
            workplace: "المديرية العامة",
            battalion: "كتيبة الحماية و الأمن",
            leaveType: "عطلة مرضية (أمومة)",
            duration: 98,
            startDate: "2024-02-01",
            endDate: "2024-05-08"
        }
    ];

    // Sample annual leaves
    const sampleAnnualLeaves = [
        {
            firstName: "محمد",
            lastName: "بن علي",
            rank: "ض ش ر",
            personalNumber: "12347",
            workplace: "المديرية العامة",
            battalion: "كتيبة حركة المرور و أمن الطرقات",
            leaveType: "عطلة سنوية",
            year: "2024",
            duration: 30,
            startDate: "2024-07-01",
            endDate: "2024-07-30"
        }
    ];

    // Sample penalties
    const samplePenalties = [
        {
            firstName: "محمد",
            lastName: "بن علي",
            rank: "ض ش ر",
            personalNumber: "12347",
            workplace: "المديرية العامة",
            battalion: "كتيبة حركة المرور و أمن الطرقات",
            penaltyType: "إنذار كتابي",
            degree: "1",
            incidentDate: "2024-01-10",
            penaltyGiven: "إنذار كتابي بسبب التأخير المتكرر",
            decisionNumber: "001/2024",
            decisionDate: "2024-01-15",
            notificationNumber: "001/2024",
            notificationDate: "2024-01-16"
        }
    ];

    // Sample absences
    const sampleAbsences = [
        {
            firstName: "أحمد",
            lastName: "محمد",
            rank: "عميد ش",
            personalNumber: "12345",
            workplace: "المديرية العامة",
            battalion: "هياكل الدعم الإداري",
            absenceReason: "ظروف عائلية طارئة",
            absenceDate: "2024-01-05",
            absenceCount: 1,
            deducted: false,
            archived: true
        }
    ];

    // Sample training courses
    const sampleTraining = [
        {
            firstName: "فاطمة",
            lastName: "علي",
            rank: "محا ش",
            personalNumber: "12346",
            workplace: "المديرية العامة",
            battalion: "كتيبة الحماية و الأمن",
            trainingType: "دورة تكوينية عادية",
            duration: "5 أيام",
            field: "الأمن والحماية",
            startDate: "2024-03-01",
            endDate: "2024-03-05",
            location: "المدرسة العليا للشرطة",
            level: "المستوى المركزي"
        }
    ];

    // Sample vacations
    const sampleVacations = [
        {
            firstName: "أحمد",
            lastName: "محمد",
            rank: "عميد ش",
            personalNumber: "12345",
            workplace: "المديرية العامة",
            battalion: "هياكل الدعم الإداري",
            duration: 15,
            startDate: "2024-06-01",
            endDate: "2024-06-15"
        }
    ];

    // Sample evaluations
    const sampleEvaluations = [
        {
            firstName: "أحمد",
            lastName: "محمد",
            rank: "عميد ش",
            personalNumber: "12345",
            workplace: "المديرية العامة",
            battalion: "هياكل الدعم الإداري",
            professionalTitle: "رئيس مصلحة",
            birthDate: "1980-05-15",
            age: 44,
            maritalStatus: "متزوج (ة)",
            children: 2,
            evaluationYear: "2024",
            score: 6,
            comment: "الأداء المهني للموظف في سنة 2024 كان جيداً ومرضياً. الانضباط في العمل ممتاز ويُحتذى به. المردود المهني للموظف جيد ويساهم في تحقيق الأهداف. يُنصح بالاستمرار على هذا المستوى مع التطوير المستمر."
        }
    ];

    // Load data into localStorage
    try {
        // Only load if no data exists
        if (!localStorage.getItem('employees') || JSON.parse(localStorage.getItem('employees')).length === 0) {
            sampleEmployees.forEach(emp => dataManager.create('employees', emp));
        }
        
        if (!localStorage.getItem('sickLeave') || JSON.parse(localStorage.getItem('sickLeave')).length === 0) {
            sampleSickLeaves.forEach(leave => dataManager.create('sickLeave', leave));
        }
        
        if (!localStorage.getItem('annualLeave') || JSON.parse(localStorage.getItem('annualLeave')).length === 0) {
            sampleAnnualLeaves.forEach(leave => dataManager.create('annualLeave', leave));
        }
        
        if (!localStorage.getItem('penalties') || JSON.parse(localStorage.getItem('penalties')).length === 0) {
            samplePenalties.forEach(penalty => dataManager.create('penalties', penalty));
        }
        
        if (!localStorage.getItem('absences') || JSON.parse(localStorage.getItem('absences')).length === 0) {
            sampleAbsences.forEach(absence => dataManager.create('absences', absence));
        }
        
        if (!localStorage.getItem('training') || JSON.parse(localStorage.getItem('training')).length === 0) {
            sampleTraining.forEach(training => dataManager.create('training', training));
        }
        
        if (!localStorage.getItem('vacations') || JSON.parse(localStorage.getItem('vacations')).length === 0) {
            sampleVacations.forEach(vacation => dataManager.create('vacations', vacation));
        }
        
        if (!localStorage.getItem('evaluations') || JSON.parse(localStorage.getItem('evaluations')).length === 0) {
            sampleEvaluations.forEach(evaluation => dataManager.create('evaluations', evaluation));
        }
        
        console.log('Demo data loaded successfully');
    } catch (error) {
        console.error('Error loading demo data:', error);
    }
}

// Function to clear all data
function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        const dataTypes = ['employees', 'sickLeave', 'annualLeave', 'penalties', 'absences', 'training', 'vacations', 'evaluations'];
        dataTypes.forEach(type => {
            localStorage.removeItem(type);
        });
        alert('تم حذف جميع البيانات بنجاح');
        location.reload();
    }
}

// Auto-load demo data when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Load demo data after a short delay to ensure dataManager is initialized
    setTimeout(loadDemoData, 1000);
});
