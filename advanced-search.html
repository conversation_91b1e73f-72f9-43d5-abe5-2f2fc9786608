<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث المتقدم - نظام إدارة بيانات الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .search-input-group {
            position: relative;
            margin-bottom: 20px;
        }
        
        .search-input {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 50px 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
        }
        
        .search-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            color: white;
            padding: 8px 15px;
        }
        
        .filter-section {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .result-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .result-card:hover {
            transform: translateY(-3px);
        }
        
        .result-type {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .relevance-score {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .suggestion-item {
            background: rgba(108, 92, 231, 0.1);
            border: 1px solid rgba(108, 92, 231, 0.3);
            border-radius: 20px;
            padding: 5px 15px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .suggestion-item:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .history-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-left: 3px solid var(--secondary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .history-item:hover {
            background: rgba(108, 92, 231, 0.1);
        }
    </style>
</head>
<body class="main-body">
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="bi bi-search"></i>
                <h1>البحث المتقدم</h1>
            </div>
            
            <div class="user-info">
                <a href="main.html" class="btn btn-primary">
                    <i class="bi bi-house-fill"></i>
                    الرئيسية
                </a>
                <a href="dashboard.html" class="btn btn-info">
                    <i class="bi bi-speedometer2"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <main class="dashboard">
        <!-- Search Container -->
        <div class="search-container">
            <div class="search-input-group">
                <input type="text" class="form-control search-input" id="mainSearchInput" 
                       placeholder="ابحث في جميع البيانات... (الاسم، رقم الذاتية، الرتبة، إلخ)"
                       onkeyup="performSmartSearch(this.value)" autocomplete="off">
                <button class="search-btn" onclick="performSmartSearch(document.getElementById('mainSearchInput').value)">
                    <i class="bi bi-search"></i>
                </button>
            </div>
            
            <!-- Search Suggestions -->
            <div id="searchSuggestions" class="mb-3" style="display: none;">
                <h6><i class="bi bi-lightbulb"></i> اقتراحات البحث:</h6>
                <div id="suggestionsContainer"></div>
            </div>
            
            <!-- Advanced Filters -->
            <div class="filter-section">
                <h5><i class="bi bi-funnel"></i> فلاتر متقدمة</h5>
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">الرتبة</label>
                        <select class="form-control" id="rankFilter">
                            <option value="">جميع الرتب</option>
                            <option value="عميد ش">عميد ش</option>
                            <option value="محا ش">محا ش</option>
                            <option value="ض ش ر">ض ش ر</option>
                            <option value="ض ش">ض ش</option>
                            <option value="ح أ ش">ح أ ش</option>
                            <option value="ح ش">ح ش</option>
                            <option value="ع ش">ع ش</option>
                            <option value="ع شبهي">ع شبهي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الكتيبة</label>
                        <select class="form-control" id="battalionFilter">
                            <option value="">جميع الكتائب</option>
                            <option value="هياكل الدعم الإداري">هياكل الدعم الإداري</option>
                            <option value="كتيبة الحماية و الأمن">كتيبة الحماية و الأمن</option>
                            <option value="كتيبة حركة المرور و أمن الطرقات">كتيبة حركة المرور و أمن الطرقات</option>
                            <option value="كتيبة الطريق العام">كتيبة الطريق العام</option>
                            <option value="كتيبة التدخل السريع 01">كتيبة التدخل السريع 01</option>
                            <option value="كتيبة التدخل السريع 02">كتيبة التدخل السريع 02</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFromFilter">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateToFilter">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="bi bi-funnel-fill"></i>
                            تطبيق الفلاتر
                        </button>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="bi bi-x-circle"></i>
                            مسح الفلاتر
                        </button>
                        <button class="btn btn-success" onclick="exportResults()">
                            <i class="bi bi-download"></i>
                            تصدير النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results and History -->
        <div class="row">
            <div class="col-md-8">
                <!-- Search Results -->
                <div class="search-container">
                    <h5><i class="bi bi-list-ul"></i> نتائج البحث <span id="resultsCount" class="badge bg-primary">0</span></h5>
                    <div id="searchResults">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-search" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p>ابدأ البحث لعرض النتائج</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- Search History -->
                <div class="search-container">
                    <h5><i class="bi bi-clock-history"></i> تاريخ البحث</h5>
                    <div id="searchHistory">
                        <!-- History items will be populated here -->
                    </div>
                    <button class="btn btn-outline-danger btn-sm w-100" onclick="clearSearchHistory()">
                        <i class="bi bi-trash"></i>
                        مسح التاريخ
                    </button>
                </div>
                
                <!-- Search Analytics -->
                <div class="search-container">
                    <h5><i class="bi bi-graph-up"></i> إحصائيات البحث</h5>
                    <div id="searchAnalytics">
                        <!-- Analytics will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data-manager.js"></script>
    <script src="statistics.js"></script>
    <script src="advanced-search.js"></script>
    <script src="search-interface.js"></script>

    <script>
        // Check authentication
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'index.html';
        }

        // Initialize search interface
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearchInterface();
        });
    </script>
</body>
</html>
