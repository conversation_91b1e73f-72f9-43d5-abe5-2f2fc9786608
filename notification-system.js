// Smart Notification System
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.rules = [];
        this.settings = {
            enabled: true,
            soundEnabled: true,
            desktopEnabled: false,
            emailEnabled: false,
            checkInterval: 60000, // 1 minute
            maxNotifications: 50
        };
        this.init();
    }

    // Initialize notification system
    init() {
        this.loadSettings();
        this.loadNotifications();
        this.setupNotificationRules();
        this.requestPermissions();
        this.startMonitoring();
        this.createNotificationContainer();
    }

    // Load settings from localStorage
    loadSettings() {
        const saved = localStorage.getItem('notificationSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // Save settings to localStorage
    saveSettings() {
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    // Load notifications from localStorage
    loadNotifications() {
        const saved = localStorage.getItem('notifications');
        if (saved) {
            this.notifications = JSON.parse(saved);
        }
    }

    // Save notifications to localStorage
    saveNotifications() {
        // Keep only recent notifications
        this.notifications = this.notifications
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, this.settings.maxNotifications);
        
        localStorage.setItem('notifications', JSON.stringify(this.notifications));
    }

    // Setup notification rules
    setupNotificationRules() {
        this.rules = [
            {
                id: 'leave_expiring',
                name: 'عطلة تنتهي قريباً',
                condition: (data) => this.checkExpiringLeaves(data),
                priority: 'high',
                category: 'leaves'
            },
            {
                id: 'penalty_notification_pending',
                name: 'عقوبة تحتاج تبليغ',
                condition: (data) => this.checkPendingPenaltyNotifications(data),
                priority: 'urgent',
                category: 'penalties'
            },
            {
                id: 'excessive_absences',
                name: 'غيابات مفرطة',
                condition: (data) => this.checkExcessiveAbsences(data),
                priority: 'medium',
                category: 'absences'
            },
            {
                id: 'evaluation_due',
                name: 'تقييم سنوي مستحق',
                condition: (data) => this.checkEvaluationDue(data),
                priority: 'medium',
                category: 'evaluations'
            },
            {
                id: 'training_reminder',
                name: 'تذكير بالدورة التكوينية',
                condition: (data) => this.checkTrainingReminder(data),
                priority: 'low',
                category: 'training'
            },
            {
                id: 'birthday_reminder',
                name: 'عيد ميلاد موظف',
                condition: (data) => this.checkBirthdayReminder(data),
                priority: 'low',
                category: 'personal'
            },
            {
                id: 'data_backup_reminder',
                name: 'تذكير بالنسخ الاحتياطي',
                condition: (data) => this.checkBackupReminder(data),
                priority: 'medium',
                category: 'system'
            }
        ];
    }

    // Request browser permissions
    async requestPermissions() {
        if ('Notification' in window && this.settings.desktopEnabled) {
            if (Notification.permission === 'default') {
                await Notification.requestPermission();
            }
        }
    }

    // Start monitoring for notifications
    startMonitoring() {
        if (!this.settings.enabled) return;

        // Initial check
        this.checkAllRules();

        // Set up interval checking
        setInterval(() => {
            this.checkAllRules();
        }, this.settings.checkInterval);
    }

    // Check all notification rules
    checkAllRules() {
        const data = this.gatherSystemData();
        
        this.rules.forEach(rule => {
            try {
                const result = rule.condition(data);
                if (result && result.length > 0) {
                    result.forEach(notification => {
                        this.createNotification({
                            ...notification,
                            ruleId: rule.id,
                            ruleName: rule.name,
                            priority: rule.priority,
                            category: rule.category
                        });
                    });
                }
            } catch (error) {
                console.error(`Error checking rule ${rule.id}:`, error);
            }
        });
    }

    // Gather system data for rule checking
    gatherSystemData() {
        return {
            employees: dataManager.getAll('employees'),
            sickLeave: dataManager.getAll('sickLeave'),
            annualLeave: dataManager.getAll('annualLeave'),
            penalties: dataManager.getAll('penalties'),
            absences: dataManager.getAll('absences'),
            training: dataManager.getAll('training'),
            vacations: dataManager.getAll('vacations'),
            evaluations: dataManager.getAll('evaluations'),
            currentDate: new Date()
        };
    }

    // Rule: Check expiring leaves
    checkExpiringLeaves(data) {
        const notifications = [];
        const today = new Date();
        const threeDaysFromNow = new Date(today.getTime() + (3 * 24 * 60 * 60 * 1000));

        [...data.sickLeave, ...data.annualLeave, ...data.vacations].forEach(leave => {
            if (leave.startDate && leave.duration) {
                const remaining = dataManager.calculateRemainingDays(leave.startDate, leave.duration);
                if (remaining > 0 && remaining <= 3) {
                    notifications.push({
                        title: 'عطلة تنتهي قريباً',
                        message: `عطلة ${leave.firstName} ${leave.lastName} تنتهي خلال ${remaining} أيام`,
                        data: leave,
                        actionUrl: 'main.html'
                    });
                }
            }
        });

        return notifications;
    }

    // Rule: Check pending penalty notifications
    checkPendingPenaltyNotifications(data) {
        const notifications = [];

        data.penalties.forEach(penalty => {
            if (!penalty.notificationDate) {
                notifications.push({
                    title: 'عقوبة تحتاج تبليغ',
                    message: `عقوبة ${penalty.firstName} ${penalty.lastName} تحتاج إلى إثبات تبليغ`,
                    data: penalty,
                    actionUrl: 'main.html'
                });
            }
        });

        return notifications;
    }

    // Rule: Check excessive absences
    checkExcessiveAbsences(data) {
        const notifications = [];
        const employeeAbsences = {};

        data.absences.forEach(absence => {
            if (!employeeAbsences[absence.personalNumber]) {
                employeeAbsences[absence.personalNumber] = [];
            }
            employeeAbsences[absence.personalNumber].push(absence);
        });

        Object.keys(employeeAbsences).forEach(personalNumber => {
            const absences = employeeAbsences[personalNumber];
            if (absences.length > 5) {
                const employee = data.employees.find(emp => emp.personalNumber === personalNumber);
                if (employee) {
                    notifications.push({
                        title: 'غيابات مفرطة',
                        message: `${employee.firstName} ${employee.lastName} لديه ${absences.length} غيابات`,
                        data: { employee, absences },
                        actionUrl: 'main.html'
                    });
                }
            }
        });

        return notifications;
    }

    // Rule: Check evaluation due
    checkEvaluationDue(data) {
        const notifications = [];
        const currentYear = new Date().getFullYear();

        data.employees.forEach(employee => {
            const hasCurrentYearEvaluation = data.evaluations.some(eval => 
                eval.personalNumber === employee.personalNumber && 
                eval.evaluationYear == currentYear
            );

            if (!hasCurrentYearEvaluation) {
                notifications.push({
                    title: 'تقييم سنوي مستحق',
                    message: `${employee.firstName} ${employee.lastName} يحتاج تقييم سنوي لعام ${currentYear}`,
                    data: employee,
                    actionUrl: 'main.html'
                });
            }
        });

        return notifications;
    }

    // Rule: Check training reminder
    checkTrainingReminder(data) {
        const notifications = [];
        const today = new Date();
        const threeDaysFromNow = new Date(today.getTime() + (3 * 24 * 60 * 60 * 1000));

        data.training.forEach(training => {
            if (training.startDate) {
                const startDate = new Date(training.startDate);
                if (startDate >= today && startDate <= threeDaysFromNow) {
                    notifications.push({
                        title: 'تذكير بالدورة التكوينية',
                        message: `دورة ${training.field} تبدأ في ${training.startDate}`,
                        data: training,
                        actionUrl: 'main.html'
                    });
                }
            }
        });

        return notifications;
    }

    // Rule: Check birthday reminder
    checkBirthdayReminder(data) {
        const notifications = [];
        const today = new Date();
        const todayMonth = today.getMonth() + 1;
        const todayDay = today.getDate();

        data.employees.forEach(employee => {
            if (employee.birthDate) {
                const birthDate = new Date(employee.birthDate);
                const birthMonth = birthDate.getMonth() + 1;
                const birthDay = birthDate.getDate();

                if (birthMonth === todayMonth && birthDay === todayDay) {
                    notifications.push({
                        title: 'عيد ميلاد موظف',
                        message: `اليوم عيد ميلاد ${employee.firstName} ${employee.lastName}`,
                        data: employee,
                        actionUrl: 'main.html'
                    });
                }
            }
        });

        return notifications;
    }

    // Rule: Check backup reminder
    checkBackupReminder(data) {
        const notifications = [];
        const lastBackup = localStorage.getItem('lastBackupTime');
        const now = Date.now();
        const oneDayAgo = now - (24 * 60 * 60 * 1000);

        if (!lastBackup || parseInt(lastBackup) < oneDayAgo) {
            notifications.push({
                title: 'تذكير بالنسخ الاحتياطي',
                message: 'لم يتم إنشاء نسخة احتياطية منذ أكثر من 24 ساعة',
                data: { lastBackup },
                actionUrl: 'dashboard.html'
            });
        }

        return notifications;
    }

    // Create notification
    createNotification(notificationData) {
        // Check if notification already exists
        const exists = this.notifications.some(n => 
            n.ruleId === notificationData.ruleId && 
            JSON.stringify(n.data) === JSON.stringify(notificationData.data)
        );

        if (exists) return;

        const notification = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            read: false,
            dismissed: false,
            ...notificationData
        };

        this.notifications.unshift(notification);
        this.saveNotifications();

        // Show notification
        this.showNotification(notification);

        return notification;
    }

    // Show notification
    showNotification(notification) {
        // Browser notification
        if (this.settings.desktopEnabled && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                tag: notification.id
            });
        }

        // In-app notification
        this.showInAppNotification(notification);

        // Sound notification
        if (this.settings.soundEnabled) {
            this.playNotificationSound();
        }
    }

    // Show in-app notification
    showInAppNotification(notification) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const priorityColors = {
            urgent: 'danger',
            high: 'warning',
            medium: 'info',
            low: 'secondary'
        };

        const notificationElement = document.createElement('div');
        notificationElement.className = `alert alert-${priorityColors[notification.priority]} alert-dismissible fade show notification-item`;
        notificationElement.style.cssText = `
            position: relative;
            margin-bottom: 10px;
            animation: slideInRight 0.3s ease;
        `;

        notificationElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="alert-heading mb-1">
                        <i class="bi bi-bell-fill"></i>
                        ${notification.title}
                    </h6>
                    <p class="mb-1">${notification.message}</p>
                    <small class="text-muted">${new Date(notification.timestamp).toLocaleString('ar-SA')}</small>
                </div>
                <div class="btn-group btn-group-sm">
                    ${notification.actionUrl ? `
                        <button class="btn btn-outline-primary" onclick="handleNotificationAction('${notification.id}', '${notification.actionUrl}')">
                            <i class="bi bi-arrow-right"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-outline-secondary" onclick="dismissNotification('${notification.id}')">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(notificationElement);

        // Auto-dismiss after 10 seconds for low priority
        if (notification.priority === 'low') {
            setTimeout(() => {
                this.dismissNotification(notification.id);
            }, 10000);
        }
    }

    // Create notification container
    createNotificationContainer() {
        if (document.getElementById('notificationContainer')) return;

        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 350px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 9999;
        `;

        document.body.appendChild(container);
    }

    // Play notification sound
    playNotificationSound() {
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.play().catch(() => {
                // Ignore audio play errors
            });
        } catch (error) {
            // Ignore audio errors
        }
    }

    // Dismiss notification
    dismissNotification(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.dismissed = true;
            this.saveNotifications();
        }

        const element = document.querySelector(`[onclick*="${notificationId}"]`);
        if (element) {
            const notificationElement = element.closest('.notification-item');
            if (notificationElement) {
                notificationElement.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    notificationElement.remove();
                }, 300);
            }
        }
    }

    // Handle notification action
    handleNotificationAction(notificationId, actionUrl) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.saveNotifications();
        }

        this.dismissNotification(notificationId);
        
        if (actionUrl) {
            window.location.href = actionUrl;
        }
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Get unread notifications count
    getUnreadCount() {
        return this.notifications.filter(n => !n.read && !n.dismissed).length;
    }

    // Get all notifications
    getAllNotifications() {
        return this.notifications.filter(n => !n.dismissed);
    }

    // Mark all as read
    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.saveNotifications();
    }

    // Clear all notifications
    clearAllNotifications() {
        this.notifications = [];
        this.saveNotifications();
        
        const container = document.getElementById('notificationContainer');
        if (container) {
            container.innerHTML = '';
        }
    }

    // Update settings
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }
}

// Global functions
function dismissNotification(notificationId) {
    if (window.notificationSystem) {
        window.notificationSystem.dismissNotification(notificationId);
    }
}

function handleNotificationAction(notificationId, actionUrl) {
    if (window.notificationSystem) {
        window.notificationSystem.handleNotificationAction(notificationId, actionUrl);
    }
}

// Initialize notification system
const notificationSystem = new NotificationSystem();
window.notificationSystem = notificationSystem;

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
