<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة بيانات الموظفين - الواجهة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="main-body">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="bi bi-building"></i>
                <h1>نظام إدارة بيانات الموظفين</h1>
            </div>
            
            <div class="datetime">
                <div class="date" id="currentDate"></div>
                <div class="time" id="currentTime"></div>
            </div>
            
            <div class="user-info">
                <span>مرحباً، ISMAIL</span>
                <a href="help.html" class="btn btn-success btn-sm me-2" title="دليل الاستخدام">
                    <i class="bi bi-question-circle"></i>
                    المساعدة
                </a>
                <button class="btn btn-info btn-sm me-2" onclick="loadDemoData()" title="تحميل بيانات تجريبية">
                    <i class="bi bi-database-fill"></i>
                    بيانات تجريبية
                </button>
                <button class="btn btn-warning btn-sm me-2" onclick="clearAllData()" title="مسح جميع البيانات">
                    <i class="bi bi-trash3-fill"></i>
                    مسح البيانات
                </button>
                <button class="logout-btn" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard -->
    <main class="dashboard">
        <div class="dashboard-grid">
            <!-- بيانات الموظفين -->
            <div class="dashboard-card fade-in" onclick="openModal('employeeModal')">
                <i class="bi bi-people-fill card-icon"></i>
                <h3 class="card-title">بيانات الموظفين</h3>
                <p class="card-description">إدارة البيانات الشخصية والوظيفية للموظفين</p>
            </div>

            <!-- العطل المرضية -->
            <div class="dashboard-card fade-in" onclick="openModal('sickLeaveModal')">
                <i class="bi bi-heart-pulse-fill card-icon"></i>
                <h3 class="card-title">العطل المرضية</h3>
                <p class="card-description">تسجيل ومتابعة العطل المرضية للموظفين</p>
            </div>

            <!-- العطل السنوية والاستثنائية -->
            <div class="dashboard-card fade-in" onclick="openModal('annualLeaveModal')">
                <i class="bi bi-calendar-check-fill card-icon"></i>
                <h3 class="card-title">العطل السنوية والاستثنائية</h3>
                <p class="card-description">إدارة العطل السنوية والاستثنائية</p>
            </div>

            <!-- العقوبات -->
            <div class="dashboard-card fade-in" onclick="openModal('penaltiesModal')">
                <i class="bi bi-exclamation-triangle-fill card-icon"></i>
                <h3 class="card-title">العقوبات</h3>
                <p class="card-description">تسجيل ومتابعة العقوبات التأديبية</p>
            </div>

            <!-- الغيابات -->
            <div class="dashboard-card fade-in" onclick="openModal('absenceModal')">
                <i class="bi bi-person-x-fill card-icon"></i>
                <h3 class="card-title">الغيابات</h3>
                <p class="card-description">تسجيل ومتابعة غيابات الموظفين</p>
            </div>

            <!-- الدورات التكوينية -->
            <div class="dashboard-card fade-in" onclick="openModal('trainingModal')">
                <i class="bi bi-mortarboard-fill card-icon"></i>
                <h3 class="card-title">الدورات التكوينية</h3>
                <p class="card-description">إدارة الدورات التكوينية والتدريبية</p>
            </div>

            <!-- الإجازات -->
            <div class="dashboard-card fade-in" onclick="openModal('vacationModal')">
                <i class="bi bi-airplane-fill card-icon"></i>
                <h3 class="card-title">الإجازات</h3>
                <p class="card-description">تسجيل ومتابعة إجازات الموظفين</p>
            </div>

            <!-- التنقيط السنوي -->
            <div class="dashboard-card fade-in" onclick="openModal('evaluationModal')">
                <i class="bi bi-star-fill card-icon"></i>
                <h3 class="card-title">التنقيط السنوي</h3>
                <p class="card-description">تقييم الأداء السنوي للموظفين</p>
            </div>

            <!-- الإحصائيات -->
            <div class="dashboard-card fade-in" onclick="openModal('statisticsModal')">
                <i class="bi bi-graph-up-arrow card-icon"></i>
                <h3 class="card-title">الإحصائيات</h3>
                <p class="card-description">عرض التقارير والإحصائيات التفصيلية</p>
            </div>

            <!-- عرض ملفات CSV -->
            <div class="dashboard-card fade-in" onclick="openModal('csvViewModal')">
                <i class="bi bi-file-earmark-spreadsheet-fill card-icon"></i>
                <h3 class="card-title">عرض ملفات CSV</h3>
                <p class="card-description">عرض وإدارة ملفات البيانات المصدرة</p>
            </div>

            <!-- لوحة التحكم التنفيذية -->
            <div class="dashboard-card fade-in" onclick="window.location.href='dashboard.html'">
                <i class="bi bi-speedometer2 card-icon"></i>
                <h3 class="card-title">لوحة التحكم التنفيذية</h3>
                <p class="card-description">مؤشرات الأداء والتحليلات المتقدمة</p>
            </div>

            <!-- البحث المتقدم -->
            <div class="dashboard-card fade-in" onclick="window.location.href='advanced-search.html'">
                <i class="bi bi-search-heart card-icon"></i>
                <h3 class="card-title">البحث المتقدم</h3>
                <p class="card-description">محرك بحث ذكي مع فلاتر متقدمة</p>
            </div>

            <!-- تقارير PDF -->
            <div class="dashboard-card fade-in" onclick="openModal('reportsModal')">
                <i class="bi bi-file-pdf card-icon"></i>
                <h3 class="card-title">تقارير PDF</h3>
                <p class="card-description">إنشاء تقارير احترافية بصيغة PDF</p>
            </div>

            <!-- إعدادات النظام -->
            <div class="dashboard-card fade-in" onclick="window.location.href='system-settings.html'">
                <i class="bi bi-gear-fill card-icon"></i>
                <h3 class="card-title">إعدادات النظام</h3>
                <p class="card-description">إعدادات متقدمة وإدارة النظام</p>
            </div>
        </div>
    </main>

    <!-- Modals will be added here -->
    <div id="modalsContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="data-manager.js"></script>
    <script src="statistics.js"></script>
    <script src="backup-system.js"></script>
    <script src="notification-system.js"></script>
    <script src="advanced-search.js"></script>
    <script src="pdf-reports.js"></script>
    <script src="script.js"></script>
    <script src="demo-data.js"></script>

    <script>
        // Check authentication
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'index.html';
        }

        // Update date and time in French
        function updateDateTime() {
            const now = new Date();
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                locale: 'fr-FR'
            };

            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };

            document.getElementById('currentDate').textContent =
                now.toLocaleDateString('fr-FR', dateOptions);
            document.getElementById('currentTime').textContent =
                now.toLocaleTimeString('fr-FR', timeOptions);
        }

        // Update every second
        setInterval(updateDateTime, 1000);
        updateDateTime();

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                window.location.href = 'index.html';
            }
        }

        // Open modal function
        function openModal(modalId) {
            // This will be implemented in script.js
            console.log('Opening modal:', modalId);
        }
    </script>
</body>
</html>
