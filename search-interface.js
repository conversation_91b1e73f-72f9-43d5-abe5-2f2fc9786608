// Search Interface Controller
class SearchInterface {
    constructor() {
        this.currentResults = {};
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.loadSearchHistory();
        this.loadSearchAnalytics();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Real-time search with debouncing
        const searchInput = document.getElementById('mainSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.performSmartSearch(e.target.value);
                }, 300);
            });
        }

        // Filter change listeners
        ['rankFilter', 'battalionFilter', 'dateFromFilter', 'dateToFilter'].forEach(filterId => {
            const element = document.getElementById(filterId);
            if (element) {
                element.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });
    }

    performSmartSearch(query) {
        if (!query || query.trim().length < 2) {
            this.clearResults();
            this.hideSuggestions();
            return;
        }

        const searchResult = advancedSearchEngine.smartSearch(query.trim());
        this.currentResults = searchResult.results;
        
        this.displayResults(searchResult.results);
        this.displaySuggestions(searchResult.suggestions);
        this.updateResultsCount();
    }

    displayResults(results) {
        const container = document.getElementById('searchResults');
        const allResults = [];

        // Flatten and sort results by relevance
        Object.keys(results).forEach(dataType => {
            results[dataType].forEach(item => {
                allResults.push({
                    ...item,
                    _dataType: dataType
                });
            });
        });

        // Sort by relevance score
        allResults.sort((a, b) => (b._relevanceScore || 0) - (a._relevanceScore || 0));

        if (allResults.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-search" style="font-size: 3rem; opacity: 0.3;"></i>
                    <p>لم يتم العثور على نتائج</p>
                </div>
            `;
            return;
        }

        const resultsHTML = allResults.map(item => this.createResultCard(item)).join('');
        container.innerHTML = resultsHTML;
    }

    createResultCard(item) {
        const dataTypeNames = {
            employees: 'بيانات الموظفين',
            sickLeave: 'العطل المرضية',
            annualLeave: 'العطل السنوية',
            penalties: 'العقوبات',
            absences: 'الغيابات',
            training: 'الدورات التكوينية',
            vacations: 'الإجازات',
            evaluations: 'التنقيط السنوي'
        };

        const relevanceScore = item._relevanceScore || 0;
        const matchedFields = item._matchedFields || [];

        return `
            <div class="result-card">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <span class="result-type">${dataTypeNames[item._dataType] || item._dataType}</span>
                        ${relevanceScore > 0 ? `<span class="relevance-score ms-2">${relevanceScore}%</span>` : ''}
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewDetails('${item._dataType}', '${item.id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="editItem('${item._dataType}', '${item.id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>${item.firstName || ''} ${item.lastName || ''}</strong>
                        ${item.rank ? `<br><small class="text-muted">الرتبة: ${item.rank}</small>` : ''}
                        ${item.personalNumber ? `<br><small class="text-muted">رقم الذاتية: ${item.personalNumber}</small>` : ''}
                    </div>
                    <div class="col-md-6">
                        ${item.workplace ? `<small class="text-muted">مكان العمل: ${item.workplace}</small><br>` : ''}
                        ${item.battalion ? `<small class="text-muted">الكتيبة: ${item.battalion}</small><br>` : ''}
                        ${item.startDate ? `<small class="text-muted">تاريخ البداية: ${item.startDate}</small>` : ''}
                    </div>
                </div>
                
                ${matchedFields.length > 0 ? `
                    <div class="mt-2">
                        <small class="text-muted">تطابق في: ${matchedFields.map(f => f.field).join(', ')}</small>
                    </div>
                ` : ''}
            </div>
        `;
    }

    displaySuggestions(suggestions) {
        const container = document.getElementById('suggestionsContainer');
        const suggestionsDiv = document.getElementById('searchSuggestions');

        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        const suggestionsHTML = suggestions.map(suggestion => `
            <span class="suggestion-item" onclick="applySuggestion('${suggestion}')">${suggestion}</span>
        `).join('');

        container.innerHTML = suggestionsHTML;
        suggestionsDiv.style.display = 'block';
    }

    hideSuggestions() {
        const suggestionsDiv = document.getElementById('searchSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }
    }

    applyFilters() {
        const query = document.getElementById('mainSearchInput').value;
        const filters = {
            rank: document.getElementById('rankFilter').value,
            battalion: document.getElementById('battalionFilter').value,
            dateFrom: document.getElementById('dateFromFilter').value,
            dateTo: document.getElementById('dateToFilter').value
        };

        if (query.trim()) {
            const results = advancedSearchEngine.searchWithFilters(query, filters);
            this.currentResults = results;
            this.displayResults(results);
            this.updateResultsCount();
        }
    }

    clearFilters() {
        document.getElementById('rankFilter').value = '';
        document.getElementById('battalionFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';
        
        const query = document.getElementById('mainSearchInput').value;
        if (query.trim()) {
            this.performSmartSearch(query);
        }
    }

    clearResults() {
        const container = document.getElementById('searchResults');
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-search" style="font-size: 3rem; opacity: 0.3;"></i>
                <p>ابدأ البحث لعرض النتائج</p>
            </div>
        `;
        this.updateResultsCount(0);
    }

    updateResultsCount(count = null) {
        const badge = document.getElementById('resultsCount');
        if (count === null) {
            count = Object.values(this.currentResults).reduce((total, results) => total + results.length, 0);
        }
        badge.textContent = count;
    }

    loadSearchHistory() {
        const history = advancedSearchEngine.getSearchHistory();
        const container = document.getElementById('searchHistory');

        if (history.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">لا يوجد تاريخ بحث</p>';
            return;
        }

        const historyHTML = history.slice(0, 10).map(item => `
            <div class="history-item" onclick="repeatSearch('${item.id}')">
                <div class="d-flex justify-content-between">
                    <small><strong>بحث متقدم</strong></small>
                    <small class="text-muted">${new Date(item.timestamp).toLocaleDateString('ar-SA')}</small>
                </div>
                <small class="text-muted">معايير متعددة</small>
            </div>
        `).join('');

        container.innerHTML = historyHTML;
    }

    loadSearchAnalytics() {
        const analytics = advancedSearchEngine.getSearchAnalytics();
        const container = document.getElementById('searchAnalytics');

        const analyticsHTML = `
            <div class="row text-center">
                <div class="col-6">
                    <h4 class="text-primary">${analytics.totalSearches}</h4>
                    <small>إجمالي البحثات</small>
                </div>
                <div class="col-6">
                    <h4 class="text-success">${Object.keys(analytics.searchTrends).length}</h4>
                    <small>أيام نشطة</small>
                </div>
            </div>
            <hr>
            <h6>اتجاهات البحث الأخيرة:</h6>
            ${Object.keys(analytics.searchTrends).slice(-5).map(date => `
                <div class="d-flex justify-content-between">
                    <small>${date}</small>
                    <small class="text-primary">${analytics.searchTrends[date]} بحث</small>
                </div>
            `).join('')}
        `;

        container.innerHTML = analyticsHTML;
    }

    exportResults() {
        if (Object.keys(this.currentResults).length === 0) {
            alert('لا توجد نتائج للتصدير');
            return;
        }

        try {
            advancedSearchEngine.exportSearchResults(this.currentResults);
        } catch (error) {
            alert(error.message);
        }
    }

    clearSearchHistory() {
        if (confirm('هل أنت متأكد من مسح تاريخ البحث؟')) {
            advancedSearchEngine.clearSearchHistory();
            this.loadSearchHistory();
            this.loadSearchAnalytics();
        }
    }
}

// Global functions
function performSmartSearch(query) {
    searchInterface.performSmartSearch(query);
}

function applyFilters() {
    searchInterface.applyFilters();
}

function clearFilters() {
    searchInterface.clearFilters();
}

function exportResults() {
    searchInterface.exportResults();
}

function clearSearchHistory() {
    searchInterface.clearSearchHistory();
}

function applySuggestion(suggestion) {
    document.getElementById('mainSearchInput').value = suggestion;
    searchInterface.performSmartSearch(suggestion);
    searchInterface.hideSuggestions();
}

function viewDetails(dataType, itemId) {
    const item = dataManager.getById(dataType, itemId);
    if (item) {
        // Create modal to show details
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل العنصر</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            ${Object.keys(item).filter(key => !key.startsWith('_')).map(key => `
                                <div class="col-md-6 mb-2">
                                    <strong>${key}:</strong> ${item[key] || 'غير محدد'}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
}

function editItem(dataType, itemId) {
    // Redirect to main page and open appropriate modal
    localStorage.setItem('editItem', JSON.stringify({ dataType, itemId }));
    window.location.href = 'main.html';
}

function repeatSearch(searchId) {
    const history = advancedSearchEngine.getSearchHistory();
    const searchItem = history.find(item => item.id === searchId);
    
    if (searchItem) {
        // Apply the search criteria
        const results = advancedSearchEngine.advancedSearch(searchItem.criteria);
        searchInterface.currentResults = results;
        searchInterface.displayResults(results);
        searchInterface.updateResultsCount();
    }
}

// Initialize search interface
let searchInterface;

function initializeSearchInterface() {
    searchInterface = new SearchInterface();
}
