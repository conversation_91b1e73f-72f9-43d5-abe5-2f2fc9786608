// Main Script for Employee Management System

// Global variables
let currentModal = null;
let currentEditId = null;

// Dropdown options
const RANKS = [
    'عميد ش',
    'محا ش',
    'ض ش ر',
    'ض ش',
    'ح أ ش',
    'ح ش',
    'ع ش',
    'ع شبهي'
];

const BATTALIONS = [
    'هياكل الدعم الإداري',
    'كتيبة الحماية و الأمن',
    'كتيبة حركة المرور و أمن الطرقات',
    'كتيبة الطريق العام',
    'كتيبة التدخل السريع 01',
    'كتيبة التدخل السريع 02'
];

const MARITAL_STATUS = [
    'متزوج (ة)',
    'أعزب (ة)',
    'مطلق (ة)',
    'أرمل (ة)'
];

const BLOOD_TYPES = [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
];

const SICK_LEAVE_TYPES = [
    'عطلة مرضية عادية',
    'عطلة مرضية نفسانية',
    'عطلة مرضية (حادث عمل)',
    'عطلة مرضية (أمومة)',
    'عطلة مرضية طويلة المدى'
];

const TRAINING_TYPES = [
    'دورة تكوينية عادية',
    'دورة تكوينية للرسكلة',
    'دورة تكوينية للإتقان'
];

const TRAINING_LEVELS = [
    'المستوى المحلي',
    'المستوى الجهوي',
    'المستوى المركزي'
];

// Utility functions
function createDropdown(options, selectedValue = '') {
    return options.map(option => 
        `<option value="${option}" ${option === selectedValue ? 'selected' : ''}>${option}</option>`
    ).join('');
}

function createYearDropdown(startYear = 2023, selectedYear = '') {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = startYear; year <= currentYear + 10; year++) {
        years.push(year);
    }
    return years.map(year => 
        `<option value="${year}" ${year.toString() === selectedYear ? 'selected' : ''}>${year}</option>`
    ).join('');
}

function createScoreDropdown(selectedScore = '') {
    const scores = [4, 5, 6, 7];
    return scores.map(score => 
        `<option value="${score}" ${score.toString() === selectedScore ? 'selected' : ''}>${score}</option>`
    ).join('');
}

function createDegreeDropdown(selectedDegree = '') {
    const degrees = [1, 2, 3, 4];
    return degrees.map(degree => 
        `<option value="${degree}" ${degree.toString() === selectedDegree ? 'selected' : ''}>${degree}</option>`
    ).join('');
}

// Modal creation functions
function createEmployeeModal() {
    return `
        <div class="modal fade" id="employeeModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">بيانات الموظفين</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <form id="employeeForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الاسم</label>
                                            <input type="text" class="form-control" name="firstName" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">اللقب</label>
                                            <input type="text" class="form-control" name="lastName" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الرتبة</label>
                                            <select class="form-control" name="rank" required>
                                                <option value="">اختر الرتبة</option>
                                                ${createDropdown(RANKS)}
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">رقم الذاتية</label>
                                            <input type="text" class="form-control" name="personalNumber" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">مكان العمل</label>
                                            <input type="text" class="form-control" name="workplace" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الكتيبة</label>
                                            <select class="form-control" name="battalion" required>
                                                <option value="">اختر الكتيبة</option>
                                                ${createDropdown(BATTALIONS)}
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الحالة العائلية</label>
                                            <select class="form-control" name="maritalStatus">
                                                <option value="">اختر الحالة العائلية</option>
                                                ${createDropdown(MARITAL_STATUS)}
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">عدد الأولاد</label>
                                            <input type="number" class="form-control" name="children" min="0">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">تاريخ الميلاد</label>
                                            <input type="date" class="form-control" name="birthDate" onchange="calculateAge()">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">مكان الميلاد</label>
                                            <input type="text" class="form-control" name="birthPlace">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">السن</label>
                                            <input type="number" class="form-control" name="age" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الزمرة الدموية</label>
                                            <select class="form-control" name="bloodType">
                                                <option value="">اختر الزمرة الدموية</option>
                                                ${createDropdown(BLOOD_TYPES)}
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <div class="photo-upload-section">
                                    <label class="form-label">صورة الموظف</label>
                                    <div class="photo-upload-area" onclick="document.getElementById('photoInput').click()">
                                        <img id="employeePhoto" src="" alt="صورة الموظف" style="display: none;">
                                        <div id="photoPlaceholder">
                                            <i class="bi bi-camera-fill"></i>
                                            <p>اضغط لتحميل الصورة</p>
                                        </div>
                                    </div>
                                    <input type="file" id="photoInput" accept="image/*" style="display: none;" onchange="handlePhotoUpload(event)">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <h6>معلومات إضافية</h6>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">عنوان الإقامة</label>
                                        <textarea class="form-control" name="address" rows="2"></textarea>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">رقم س الفردي</label>
                                        <input type="text" class="form-control" name="individualNumber">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="data-list">
                            <div class="data-list-header">
                                <h6>قائمة الموظفين</h6>
                                <div class="search-box">
                                    <input type="text" class="form-control" placeholder="البحث برقم الذاتية..." onkeyup="searchEmployees(this.value)">
                                    <button class="btn btn-primary" onclick="searchEmployees()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="employeeList"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveEmployee()">حفظ</button>
                        <button type="button" class="btn btn-warning" onclick="editEmployee()">تعديل</button>
                        <button type="button" class="btn btn-danger" onclick="deleteEmployee()">حذف</button>
                        <button type="button" class="btn btn-info" onclick="exportEmployees()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create sick leave modal
function createSickLeaveModal() {
    return `
        <div class="modal fade" id="sickLeaveModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">العطل المرضية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="sickLeaveForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع العطلة المرضية</label>
                                    <select class="form-control" name="leaveType" required>
                                        <option value="">اختر نوع العطلة</option>
                                        ${createDropdown(SICK_LEAVE_TYPES)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مدة العطلة المرضية (بالأيام)</label>
                                    <input type="number" class="form-control" name="duration" min="1" required onchange="calculateEndDate()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" name="startDate" required onchange="calculateEndDate()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" name="endDate" readonly>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">الأيام المتبقية</label>
                                    <input type="number" class="form-control" name="remainingDays" readonly>
                                </div>
                            </div>
                        </form>
                        <div class="data-list">
                            <div class="data-list-header">
                                <h6>قائمة العطل المرضية</h6>
                                <div class="search-box">
                                    <input type="text" class="form-control" placeholder="البحث برقم الذاتية..." onkeyup="searchSickLeave(this.value)">
                                    <button class="btn btn-primary" onclick="searchSickLeave()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="sickLeaveList"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveSickLeave()">حفظ</button>
                        <button type="button" class="btn btn-warning" onclick="editSickLeave()">تعديل</button>
                        <button type="button" class="btn btn-danger" onclick="deleteSickLeave()">حذف</button>
                        <button type="button" class="btn btn-info" onclick="exportSickLeave()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create annual leave modal
function createAnnualLeaveModal() {
    return `
        <div class="modal fade" id="annualLeaveModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">العطل السنوية والاستثنائية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="annualLeaveForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع العطلة</label>
                                    <select class="form-control" name="leaveType" required>
                                        <option value="">اختر نوع العطلة</option>
                                        <option value="عطلة سنوية">عطلة سنوية</option>
                                        <option value="عطلة استثنائية">عطلة استثنائية</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">السنة</label>
                                    <select class="form-control" name="year" required>
                                        <option value="">اختر السنة</option>
                                        ${createYearDropdown(2023)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مدة العطلة (بالأيام)</label>
                                    <input type="number" class="form-control" name="duration" min="1" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" name="startDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" name="endDate" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الأيام المتبقية</label>
                                    <input type="number" class="form-control" name="remainingDays" readonly>
                                </div>
                            </div>
                        </form>
                        <div class="data-list">
                            <div class="data-list-header">
                                <h6>قائمة العطل السنوية والاستثنائية</h6>
                                <div class="search-box">
                                    <input type="text" class="form-control" placeholder="البحث برقم الذاتية..." onkeyup="searchAnnualLeave(this.value)">
                                    <button class="btn btn-primary" onclick="searchAnnualLeave()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="annualLeaveList"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveAnnualLeave()">حفظ</button>
                        <button type="button" class="btn btn-warning" onclick="editAnnualLeave()">تعديل</button>
                        <button type="button" class="btn btn-danger" onclick="deleteAnnualLeave()">حذف</button>
                        <button type="button" class="btn btn-info" onclick="exportAnnualLeave()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create penalties modal
function createPenaltiesModal() {
    return `
        <div class="modal fade" id="penaltiesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">العقوبات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="penaltiesForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع العقوبة</label>
                                    <input type="text" class="form-control" name="penaltyType" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الدرجة</label>
                                    <select class="form-control" name="degree" required>
                                        <option value="">اختر الدرجة</option>
                                        ${createDegreeDropdown()}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ ارتكاب الخطأ</label>
                                    <input type="date" class="form-control" name="incidentDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العقوبة الممنوحة</label>
                                    <textarea class="form-control" name="penaltyGiven" rows="2" required></textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم المقرر</label>
                                    <input type="text" class="form-control" name="decisionNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ المقرر</label>
                                    <input type="date" class="form-control" name="decisionDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم محضر إثبات التبليغ</label>
                                    <input type="text" class="form-control" name="notificationNumber">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ محضر إثبات التبليغ</label>
                                    <input type="date" class="form-control" name="notificationDate">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="savePenalty()">حفظ</button>
                        <button type="button" class="btn btn-info" onclick="exportPenalties()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create absence modal
function createAbsenceModal() {
    return `
        <div class="modal fade" id="absenceModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الغيابات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="absenceForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سبب الغياب</label>
                                    <input type="text" class="form-control" name="absenceReason" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الغياب</label>
                                    <input type="date" class="form-control" name="absenceDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">عدد الغيابات</label>
                                    <input type="number" class="form-control" name="absenceCount" min="1" value="1" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="deducted" id="deducted">
                                        <label class="form-check-label" for="deducted">
                                            للحسم (x)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="archived" id="archived">
                                        <label class="form-check-label" for="archived">
                                            للحفظ (x)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveAbsence()">حفظ</button>
                        <button type="button" class="btn btn-info" onclick="exportAbsences()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create training modal
function createTrainingModal() {
    return `
        <div class="modal fade" id="trainingModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الدورات التكوينية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="trainingForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع الدورة التكوينية</label>
                                    <select class="form-control" name="trainingType" required>
                                        <option value="">اختر نوع الدورة</option>
                                        ${createDropdown(TRAINING_TYPES)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مدة الدورة التكوينية</label>
                                    <input type="text" class="form-control" name="duration" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">دورة تكوينية في مجال</label>
                                    <input type="text" class="form-control" name="field" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ بداية الدورة</label>
                                    <input type="date" class="form-control" name="startDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ نهاية الدورة</label>
                                    <input type="date" class="form-control" name="endDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان إجراء الدورة</label>
                                    <input type="text" class="form-control" name="location" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">على المستوى</label>
                                    <select class="form-control" name="level" required>
                                        <option value="">اختر المستوى</option>
                                        ${createDropdown(TRAINING_LEVELS)}
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveTraining()">حفظ</button>
                        <button type="button" class="btn btn-info" onclick="exportTraining()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create vacation modal
function createVacationModal() {
    return `
        <div class="modal fade" id="vacationModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الإجازات</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="vacationForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مدة الإجازة (بالأيام)</label>
                                    <input type="number" class="form-control" name="duration" min="1" required onchange="calculateVacationEndDate()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" name="startDate" required onchange="calculateVacationEndDate()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" name="endDate" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الأيام المتبقية</label>
                                    <input type="number" class="form-control" name="remainingDays" readonly>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveVacation()">حفظ</button>
                        <button type="button" class="btn btn-info" onclick="exportVacations()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create evaluation modal
function createEvaluationModal() {
    return `
        <div class="modal fade" id="evaluationModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التنقيط السنوي للموظفين</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="evaluationForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" class="form-control" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللقب</label>
                                    <input type="text" class="form-control" name="lastName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control" name="rank" required>
                                        <option value="">اختر الرتبة</option>
                                        ${createDropdown(RANKS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الذاتية</label>
                                    <input type="text" class="form-control" name="personalNumber" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مكان العمل</label>
                                    <input type="text" class="form-control" name="workplace" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكتيبة</label>
                                    <select class="form-control" name="battalion" required>
                                        <option value="">اختر الكتيبة</option>
                                        ${createDropdown(BATTALIONS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الصفة المهنية</label>
                                    <input type="text" class="form-control" name="professionalTitle" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" name="birthDate" onchange="calculateEvaluationAge()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">السن</label>
                                    <input type="number" class="form-control" name="age" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الحالة العائلية</label>
                                    <select class="form-control" name="maritalStatus">
                                        <option value="">اختر الحالة العائلية</option>
                                        ${createDropdown(MARITAL_STATUS)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">عدد الأولاد</label>
                                    <input type="number" class="form-control" name="children" min="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سنة التقييم</label>
                                    <select class="form-control" name="evaluationYear" required>
                                        <option value="">اختر السنة</option>
                                        ${createYearDropdown(2024)}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">النقطة</label>
                                    <select class="form-control" name="score" required onchange="generateComment()">
                                        <option value="">اختر النقطة</option>
                                        ${createScoreDropdown()}
                                    </select>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">الملاحظة</label>
                                    <textarea class="form-control" name="comment" rows="4" readonly></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveEvaluation()">حفظ</button>
                        <button type="button" class="btn btn-info" onclick="exportEvaluations()">تصدير CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create statistics modal
function createStatisticsModal() {
    return `
        <div class="modal fade" id="statisticsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الإحصائيات والتقارير</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="statistics-summary">
                                    <h6>ملخص عام</h6>
                                    <div id="generalStats"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="stat-card">
                                    <h6>إحصائيات الموظفين</h6>
                                    <div id="employeeStats"></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card">
                                    <h6>إحصائيات العقوبات</h6>
                                    <div id="penaltyStats"></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card">
                                    <h6>إحصائيات العطل</h6>
                                    <div id="leaveStats"></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card">
                                    <h6>إحصائيات الغيابات</h6>
                                    <div id="absenceStats"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-info" onclick="exportAllStatistics()">تصدير جميع الإحصائيات</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create CSV view modal
function createCSVViewModal() {
    return `
        <div class="modal fade" id="csvViewModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">عرض ملفات CSV</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="csv-navigation">
                            <div class="btn-group mb-3" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('employees', this)">بيانات الموظفين</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('sickLeave', this)">العطل المرضية</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('annualLeave', this)">العطل السنوية</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('penalties', this)">العقوبات</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('absences', this)">الغيابات</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('training', this)">الدورات التكوينية</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('vacations', this)">الإجازات</button>
                                <button type="button" class="btn btn-outline-primary" onclick="showCSVData('evaluations', this)">التنقيط السنوي</button>
                            </div>
                        </div>
                        <div id="csvDataContainer">
                            <p class="text-center">اختر نوع البيانات لعرضها</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="importCSV()">استيراد CSV</button>
                        <button type="button" class="btn btn-info" onclick="exportCurrentCSV()">تصدير البيانات الحالية</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create reports modal
function createReportsModal() {
    return `
        <div class="modal fade" id="reportsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقارير PDF احترافية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-people-fill"></i>
                                            تقرير الموظفين
                                        </h6>
                                        <p class="card-text">تقرير شامل لجميع بيانات الموظفين</p>
                                        <button class="btn btn-primary" onclick="generateEmployeeReport()">
                                            <i class="bi bi-file-pdf"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-calendar-x"></i>
                                            تقرير العطل
                                        </h6>
                                        <p class="card-text">تقرير العطل المرضية والسنوية</p>
                                        <div class="mb-2">
                                            <select class="form-control form-control-sm" id="leaveReportType">
                                                <option value="all">جميع العطل</option>
                                                <option value="sick">العطل المرضية</option>
                                                <option value="annual">العطل السنوية</option>
                                                <option value="vacation">الإجازات</option>
                                            </select>
                                        </div>
                                        <button class="btn btn-primary" onclick="generateLeaveReport()">
                                            <i class="bi bi-file-pdf"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            تقرير العقوبات
                                        </h6>
                                        <p class="card-text">تقرير العقوبات التأديبية</p>
                                        <button class="btn btn-primary" onclick="generatePenaltyReport()">
                                            <i class="bi bi-file-pdf"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-graph-up"></i>
                                            تقرير الإحصائيات
                                        </h6>
                                        <p class="card-text">تقرير شامل للإحصائيات</p>
                                        <button class="btn btn-primary" onclick="generateStatisticsReport()">
                                            <i class="bi bi-file-pdf"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h6>تقرير مخصص</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="reportDateFrom">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="reportDateTo">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">نوع التقرير</label>
                                        <select class="form-control" id="customReportType">
                                            <option value="employees">الموظفين</option>
                                            <option value="leaves">العطل</option>
                                            <option value="penalties">العقوبات</option>
                                            <option value="absences">الغيابات</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-success" onclick="generateCustomReport()">
                                        <i class="bi bi-file-pdf"></i>
                                        إنشاء تقرير مخصص
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Main modal opening function
function openModal(modalId) {
    let modalHTML = '';

    switch (modalId) {
        case 'employeeModal':
            modalHTML = createEmployeeModal();
            break;
        case 'sickLeaveModal':
            modalHTML = createSickLeaveModal();
            break;
        case 'annualLeaveModal':
            modalHTML = createAnnualLeaveModal();
            break;
        case 'penaltiesModal':
            modalHTML = createPenaltiesModal();
            break;
        case 'absenceModal':
            modalHTML = createAbsenceModal();
            break;
        case 'trainingModal':
            modalHTML = createTrainingModal();
            break;
        case 'vacationModal':
            modalHTML = createVacationModal();
            break;
        case 'evaluationModal':
            modalHTML = createEvaluationModal();
            break;
        case 'statisticsModal':
            modalHTML = createStatisticsModal();
            break;
        case 'csvViewModal':
            modalHTML = createCSVViewModal();
            break;
        case 'reportsModal':
            modalHTML = createReportsModal();
            break;
        default:
            console.log('Modal not implemented yet:', modalId);
            return;
    }

    // Remove existing modal
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal to container
    document.getElementById('modalsContainer').innerHTML = modalHTML;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    currentModal = modal;

    // Load data for the modal
    loadModalData(modalId);
}

// Photo upload handler
function handlePhotoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.getElementById('employeePhoto');
            const placeholder = document.getElementById('photoPlaceholder');
            
            img.src = e.target.result;
            img.style.display = 'block';
            placeholder.style.display = 'none';
        };
        reader.readAsDataURL(file);
    }
}

// Calculate age from birth date
function calculateAge() {
    const birthDate = document.querySelector('input[name="birthDate"]').value;
    if (birthDate) {
        const age = dataManager.calculateAge(birthDate);
        document.querySelector('input[name="age"]').value = age;
    }
}

// Calculate end date for leaves
function calculateEndDate() {
    const startDate = document.querySelector('input[name="startDate"]').value;
    const duration = document.querySelector('input[name="duration"]').value;

    if (startDate && duration) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + parseInt(duration) - 1);

        document.querySelector('input[name="endDate"]').value = end.toISOString().split('T')[0];

        // Calculate remaining days
        const remaining = dataManager.calculateRemainingDays(startDate, duration);
        document.querySelector('input[name="remainingDays"]').value = remaining;
    }
}

// Save employee function
function saveEmployee() {
    const form = document.getElementById('employeeForm');
    const formData = new FormData(form);
    const employeeData = {};

    for (let [key, value] of formData.entries()) {
        employeeData[key] = value;
    }

    // Add photo if uploaded
    const photoImg = document.getElementById('employeePhoto');
    if (photoImg.src && photoImg.style.display !== 'none') {
        employeeData.photo = photoImg.src;
    }

    try {
        if (currentEditId) {
            dataManager.update('employees', currentEditId, employeeData);
            alert('تم تحديث بيانات الموظف بنجاح');
        } else {
            dataManager.create('employees', employeeData);
            alert('تم حفظ بيانات الموظف بنجاح');
        }

        currentModal.hide();
        currentEditId = null;
        loadModalData('employeeModal');

    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

// Save sick leave function
function saveSickLeave() {
    const form = document.getElementById('sickLeaveForm');
    const formData = new FormData(form);
    const sickLeaveData = {};

    for (let [key, value] of formData.entries()) {
        sickLeaveData[key] = value;
    }

    try {
        if (currentEditId) {
            dataManager.update('sickLeave', currentEditId, sickLeaveData);
            alert('تم تحديث بيانات العطلة المرضية بنجاح');
        } else {
            dataManager.create('sickLeave', sickLeaveData);
            alert('تم حفظ بيانات العطلة المرضية بنجاح');
        }

        currentEditId = null;
        loadModalData('sickLeaveModal');

    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

// Save annual leave function
function saveAnnualLeave() {
    const form = document.getElementById('annualLeaveForm');
    const formData = new FormData(form);
    const annualLeaveData = {};

    for (let [key, value] of formData.entries()) {
        annualLeaveData[key] = value;
    }

    try {
        if (currentEditId) {
            dataManager.update('annualLeave', currentEditId, annualLeaveData);
            alert('تم تحديث بيانات العطلة السنوية بنجاح');
        } else {
            dataManager.create('annualLeave', annualLeaveData);
            alert('تم حفظ بيانات العطلة السنوية بنجاح');
        }

        currentEditId = null;
        loadModalData('annualLeaveModal');

    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

// Load modal data
function loadModalData(modalId) {
    switch (modalId) {
        case 'employeeModal':
            loadEmployeeList();
            break;
        case 'sickLeaveModal':
            loadSickLeaveList();
            break;
        case 'annualLeaveModal':
            loadAnnualLeaveList();
            break;
        case 'statisticsModal':
            loadStatistics();
            break;
    }
}

// Load employee list
function loadEmployeeList() {
    const employees = dataManager.getAll('employees');
    const listContainer = document.getElementById('employeeList');

    if (!listContainer) return;

    if (employees.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد بيانات موظفين</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>الرتبة</th>
                    <th>رقم الذاتية</th>
                    <th>مكان العمل</th>
                    <th>الكتيبة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${employees.map(emp => `
                    <tr>
                        <td>${emp.firstName} ${emp.lastName}</td>
                        <td>${emp.rank}</td>
                        <td>${emp.personalNumber}</td>
                        <td>${emp.workplace}</td>
                        <td>${emp.battalion}</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editEmployee('${emp.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteEmployee('${emp.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

// Load sick leave list
function loadSickLeaveList() {
    const sickLeaves = dataManager.getAll('sickLeave');
    const listContainer = document.getElementById('sickLeaveList');

    if (!listContainer) return;

    if (sickLeaves.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد بيانات عطل مرضية</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>رقم الذاتية</th>
                    <th>نوع العطلة</th>
                    <th>تاريخ البداية</th>
                    <th>المدة</th>
                    <th>الأيام المتبقية</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${sickLeaves.map(leave => `
                    <tr>
                        <td>${leave.firstName} ${leave.lastName}</td>
                        <td>${leave.personalNumber}</td>
                        <td>${leave.leaveType}</td>
                        <td>${leave.startDate}</td>
                        <td>${leave.duration} يوم</td>
                        <td>${dataManager.calculateRemainingDays(leave.startDate, leave.duration)} يوم</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editSickLeave('${leave.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteSickLeave('${leave.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

// Additional save functions
function savePenalty() {
    const form = document.getElementById('penaltiesForm');
    const formData = new FormData(form);
    const penaltyData = {};

    for (let [key, value] of formData.entries()) {
        penaltyData[key] = value;
    }

    try {
        dataManager.create('penalties', penaltyData);
        alert('تم حفظ بيانات العقوبة بنجاح');
        form.reset();
    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

function saveAbsence() {
    const form = document.getElementById('absenceForm');
    const formData = new FormData(form);
    const absenceData = {};

    for (let [key, value] of formData.entries()) {
        if (key === 'deducted' || key === 'archived') {
            absenceData[key] = true;
        } else {
            absenceData[key] = value;
        }
    }

    try {
        dataManager.create('absences', absenceData);
        alert('تم حفظ بيانات الغياب بنجاح');
        form.reset();
    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

function saveTraining() {
    const form = document.getElementById('trainingForm');
    const formData = new FormData(form);
    const trainingData = {};

    for (let [key, value] of formData.entries()) {
        trainingData[key] = value;
    }

    try {
        dataManager.create('training', trainingData);
        alert('تم حفظ بيانات الدورة التكوينية بنجاح');
        form.reset();
    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

function saveVacation() {
    const form = document.getElementById('vacationForm');
    const formData = new FormData(form);
    const vacationData = {};

    for (let [key, value] of formData.entries()) {
        vacationData[key] = value;
    }

    try {
        dataManager.create('vacations', vacationData);
        alert('تم حفظ بيانات الإجازة بنجاح');
        form.reset();
    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

function saveEvaluation() {
    const form = document.getElementById('evaluationForm');
    const formData = new FormData(form);
    const evaluationData = {};

    for (let [key, value] of formData.entries()) {
        evaluationData[key] = value;
    }

    try {
        dataManager.create('evaluations', evaluationData);
        alert('تم حفظ بيانات التنقيط السنوي بنجاح');
        form.reset();
    } catch (error) {
        alert('حدث خطأ أثناء حفظ البيانات');
        console.error(error);
    }
}

// Calculate vacation end date
function calculateVacationEndDate() {
    const startDate = document.querySelector('#vacationForm input[name="startDate"]').value;
    const duration = document.querySelector('#vacationForm input[name="duration"]').value;

    if (startDate && duration) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + parseInt(duration) - 1);

        document.querySelector('#vacationForm input[name="endDate"]').value = end.toISOString().split('T')[0];

        const remaining = dataManager.calculateRemainingDays(startDate, duration);
        document.querySelector('#vacationForm input[name="remainingDays"]').value = remaining;
    }
}

// Calculate evaluation age
function calculateEvaluationAge() {
    const birthDate = document.querySelector('#evaluationForm input[name="birthDate"]').value;
    if (birthDate) {
        const age = dataManager.calculateAge(birthDate);
        document.querySelector('#evaluationForm input[name="age"]').value = age;
    }
}

// Generate evaluation comment
function generateComment() {
    const score = document.querySelector('#evaluationForm select[name="score"]').value;
    const year = document.querySelector('#evaluationForm select[name="evaluationYear"]').value;

    if (score && year) {
        const comments = dataManager.generateEvaluationComment(parseInt(score), year);
        const comment = comments.join('. ') + '.';
        document.querySelector('#evaluationForm textarea[name="comment"]').value = comment;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Employee Management System Loaded');

    // Update remaining days for all leaves daily
    updateAllRemainingDays();
});

// Update remaining days for all leaves
function updateAllRemainingDays() {
    const leaveTypes = ['sickLeave', 'annualLeave', 'vacations'];

    leaveTypes.forEach(type => {
        const leaves = dataManager.getAll(type);
        leaves.forEach(leave => {
            if (leave.startDate && leave.duration) {
                const remaining = dataManager.calculateRemainingDays(leave.startDate, leave.duration);
                leave.remainingDays = remaining;
                dataManager.update(type, leave.id, leave);
            }
        });
    });
}

// PDF Report Functions
function generateEmployeeReport() {
    if (typeof window.pdfReportsGenerator !== 'undefined') {
        window.pdfReportsGenerator.generateEmployeeReport()
            .then(() => {
                alert('تم إنشاء تقرير الموظفين بنجاح');
            })
            .catch(error => {
                alert('خطأ في إنشاء التقرير: ' + error.message);
            });
    } else {
        alert('مولد التقارير غير متاح');
    }
}

function generateLeaveReport() {
    const type = document.getElementById('leaveReportType').value;
    const dateFrom = document.getElementById('reportDateFrom').value;
    const dateTo = document.getElementById('reportDateTo').value;

    if (typeof window.pdfReportsGenerator !== 'undefined') {
        window.pdfReportsGenerator.generateLeaveReport(type, dateFrom, dateTo)
            .then(() => {
                alert('تم إنشاء تقرير العطل بنجاح');
            })
            .catch(error => {
                alert('خطأ في إنشاء التقرير: ' + error.message);
            });
    } else {
        alert('مولد التقارير غير متاح');
    }
}

function generatePenaltyReport() {
    const dateFrom = document.getElementById('reportDateFrom').value;
    const dateTo = document.getElementById('reportDateTo').value;

    if (typeof window.pdfReportsGenerator !== 'undefined') {
        window.pdfReportsGenerator.generatePenaltyReport(dateFrom, dateTo)
            .then(() => {
                alert('تم إنشاء تقرير العقوبات بنجاح');
            })
            .catch(error => {
                alert('خطأ في إنشاء التقرير: ' + error.message);
            });
    } else {
        alert('مولد التقارير غير متاح');
    }
}

function generateStatisticsReport() {
    if (typeof window.pdfReportsGenerator !== 'undefined') {
        window.pdfReportsGenerator.generateStatisticsReport()
            .then(() => {
                alert('تم إنشاء تقرير الإحصائيات بنجاح');
            })
            .catch(error => {
                alert('خطأ في إنشاء التقرير: ' + error.message);
            });
    } else {
        alert('مولد التقارير غير متاح');
    }
}

function generateCustomReport() {
    const type = document.getElementById('customReportType').value;
    const dateFrom = document.getElementById('reportDateFrom').value;
    const dateTo = document.getElementById('reportDateTo').value;

    if (!dateFrom || !dateTo) {
        alert('يرجى تحديد فترة زمنية للتقرير');
        return;
    }

    let data = [];
    let title = '';
    let columns = [];

    switch (type) {
        case 'employees':
            data = dataManager.getAll('employees');
            title = `تقرير الموظفين (${dateFrom} - ${dateTo})`;
            columns = ['firstName', 'lastName', 'rank', 'personalNumber', 'workplace', 'battalion'];
            break;
        case 'leaves':
            data = [...dataManager.getAll('sickLeave'), ...dataManager.getAll('annualLeave')];
            title = `تقرير العطل (${dateFrom} - ${dateTo})`;
            columns = ['firstName', 'lastName', 'leaveType', 'startDate', 'duration'];
            break;
        case 'penalties':
            data = dataManager.getAll('penalties');
            title = `تقرير العقوبات (${dateFrom} - ${dateTo})`;
            columns = ['firstName', 'lastName', 'penaltyType', 'degree', 'incidentDate'];
            break;
        case 'absences':
            data = dataManager.getAll('absences');
            title = `تقرير الغيابات (${dateFrom} - ${dateTo})`;
            columns = ['firstName', 'lastName', 'absenceReason', 'absenceDate', 'absenceCount'];
            break;
    }

    // Filter by date range
    if (dateFrom && dateTo) {
        const from = new Date(dateFrom);
        const to = new Date(dateTo);
        data = data.filter(item => {
            const dateFields = ['startDate', 'incidentDate', 'absenceDate', 'appointmentDate'];
            return dateFields.some(field => {
                if (item[field]) {
                    const itemDate = new Date(item[field]);
                    return itemDate >= from && itemDate <= to;
                }
                return false;
            });
        });
    }

    if (typeof window.pdfReportsGenerator !== 'undefined') {
        window.pdfReportsGenerator.generateCustomReport(title, data, columns)
            .then(() => {
                alert('تم إنشاء التقرير المخصص بنجاح');
            })
            .catch(error => {
                alert('خطأ في إنشاء التقرير: ' + error.message);
            });
    } else {
        alert('مولد التقارير غير متاح');
    }
}

// Export functions
function exportEmployees() {
    dataManager.exportToCSV('employees', 'employees.csv');
}

function exportSickLeave() {
    dataManager.exportToCSV('sickLeave', 'sick_leave.csv');
}

function exportAnnualLeave() {
    dataManager.exportToCSV('annualLeave', 'annual_leave.csv');
}

function exportPenalties() {
    dataManager.exportToCSV('penalties', 'penalties.csv');
}

function exportAbsences() {
    dataManager.exportToCSV('absences', 'absences.csv');
}

function exportTraining() {
    dataManager.exportToCSV('training', 'training.csv');
}

function exportVacations() {
    dataManager.exportToCSV('vacations', 'vacations.csv');
}

function exportEvaluations() {
    dataManager.exportToCSV('evaluations', 'evaluations.csv');
}

// Statistics functions
function loadStatistics() {
    const stats = dataManager.getStatistics();

    // General statistics
    const generalStatsHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-label">إجمالي الموظفين</span>
                    <span class="stat-value">${stats.totalEmployees}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-label">العطل المرضية النشطة</span>
                    <span class="stat-value">${stats.activeSickLeave}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-label">العطل السنوية النشطة</span>
                    <span class="stat-value">${stats.activeAnnualLeave}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-label">إجمالي العقوبات</span>
                    <span class="stat-value">${stats.totalPenalties}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('generalStats').innerHTML = generalStatsHTML;

    // Employee statistics
    const employeeStats = statisticsManager.getEmployeesByRank();
    const employeeStatsHTML = Object.keys(employeeStats).map(rank => `
        <div class="stat-item">
            <span class="stat-label">${rank}</span>
            <span class="stat-value">${employeeStats[rank]}</span>
        </div>
    `).join('');

    document.getElementById('employeeStats').innerHTML = employeeStatsHTML;

    // Penalty statistics
    const penaltyStats = statisticsManager.getPenaltiesByPeriod('month');
    const penaltyStatsHTML = Object.keys(penaltyStats).slice(0, 5).map(month => `
        <div class="stat-item">
            <span class="stat-label">${month}</span>
            <span class="stat-value">${penaltyStats[month]}</span>
        </div>
    `).join('');

    document.getElementById('penaltyStats').innerHTML = penaltyStatsHTML || '<p>لا توجد بيانات عقوبات</p>';

    // Leave statistics
    const leaveStats = statisticsManager.getAnnualLeaveByPeriod('month');
    const leaveStatsHTML = Object.keys(leaveStats).slice(0, 5).map(month => `
        <div class="stat-item">
            <span class="stat-label">${month}</span>
            <span class="stat-value">${leaveStats[month]}</span>
        </div>
    `).join('');

    document.getElementById('leaveStats').innerHTML = leaveStatsHTML || '<p>لا توجد بيانات عطل</p>';

    // Absence statistics
    const absenceStats = statisticsManager.getAbsencesByEmployeeAndPeriod('month');
    const absenceStatsHTML = Object.keys(absenceStats).slice(0, 5).map(key => `
        <div class="stat-item">
            <span class="stat-label">${key}</span>
            <span class="stat-value">${absenceStats[key]}</span>
        </div>
    `).join('');

    document.getElementById('absenceStats').innerHTML = absenceStatsHTML || '<p>لا توجد بيانات غيابات</p>';
}

// Load annual leave list
function loadAnnualLeaveList() {
    const annualLeaves = dataManager.getAll('annualLeave');
    const listContainer = document.getElementById('annualLeaveList');

    if (!listContainer) return;

    if (annualLeaves.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد بيانات عطل سنوية</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>رقم الذاتية</th>
                    <th>نوع العطلة</th>
                    <th>السنة</th>
                    <th>تاريخ البداية</th>
                    <th>المدة</th>
                    <th>الأيام المتبقية</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${annualLeaves.map(leave => `
                    <tr>
                        <td>${leave.firstName} ${leave.lastName}</td>
                        <td>${leave.personalNumber}</td>
                        <td>${leave.leaveType}</td>
                        <td>${leave.year}</td>
                        <td>${leave.startDate}</td>
                        <td>${leave.duration} يوم</td>
                        <td>${dataManager.calculateRemainingDays(leave.startDate, leave.duration)} يوم</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editAnnualLeave('${leave.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteAnnualLeave('${leave.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

// CSV view functions
function showCSVData(dataType, buttonElement) {
    const data = dataManager.getAll(dataType);
    const container = document.getElementById('csvDataContainer');

    // Update active button
    document.querySelectorAll('.csv-navigation .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    if (buttonElement) {
        buttonElement.classList.add('active');
    }

    if (data.length === 0) {
        container.innerHTML = '<p class="text-center">لا توجد بيانات</p>';
        return;
    }

    const headers = Object.keys(data[0]);
    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    ${headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.map(row => `
                    <tr>
                        ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = tableHTML;
}

// Search functions
function searchSickLeave(query) {
    if (!query) {
        loadSickLeaveList();
        return;
    }

    const results = dataManager.search('sickLeave', 'personalNumber', query);
    const listContainer = document.getElementById('sickLeaveList');

    if (results.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد نتائج</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>رقم الذاتية</th>
                    <th>نوع العطلة</th>
                    <th>تاريخ البداية</th>
                    <th>المدة</th>
                    <th>الأيام المتبقية</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${results.map(leave => `
                    <tr>
                        <td>${leave.firstName} ${leave.lastName}</td>
                        <td>${leave.personalNumber}</td>
                        <td>${leave.leaveType}</td>
                        <td>${leave.startDate}</td>
                        <td>${leave.duration} يوم</td>
                        <td>${dataManager.calculateRemainingDays(leave.startDate, leave.duration)} يوم</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editSickLeave('${leave.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteSickLeave('${leave.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

// Import/Export functions
function importCSV() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const dataType = prompt('أدخل نوع البيانات (employees, sickLeave, annualLeave, penalties, absences, training, vacations, evaluations):');
            if (dataType) {
                dataManager.importFromCSV(dataType, file)
                    .then(() => {
                        alert('تم استيراد البيانات بنجاح');
                        showCSVData(dataType);
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء استيراد البيانات');
                        console.error(error);
                    });
            }
        }
    };
    input.click();
}

function exportCurrentCSV() {
    const activeBtn = document.querySelector('.csv-navigation .btn.active');
    if (activeBtn) {
        const dataType = activeBtn.textContent.trim();
        dataManager.exportToCSV(dataType, `${dataType}.csv`);
    } else {
        alert('يرجى اختيار نوع البيانات أولاً');
    }
}

function exportAllStatistics() {
    const report = statisticsManager.generateComprehensiveReport();
    const csvData = [];

    Object.keys(report).forEach(category => {
        Object.keys(report[category]).forEach(subcategory => {
            Object.keys(report[category][subcategory]).forEach(item => {
                csvData.push({
                    category: category,
                    subcategory: subcategory,
                    item: item,
                    count: report[category][subcategory][item]
                });
            });
        });
    });

    // Create CSV content
    const headers = ['category', 'subcategory', 'item', 'count'];
    const csvContent = [
        headers.join(','),
        ...csvData.map(row =>
            headers.map(header => `"${row[header]}"`).join(',')
        )
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `statistics_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Search functions
function searchEmployees(query) {
    if (!query) {
        loadEmployeeList();
        return;
    }

    const results = dataManager.search('employees', 'personalNumber', query);
    displayEmployeeResults(results);
}

function searchAnnualLeave(query) {
    if (!query) {
        loadAnnualLeaveList();
        return;
    }

    const results = dataManager.search('annualLeave', 'personalNumber', query);
    displayAnnualLeaveResults(results);
}

function displayEmployeeResults(results) {
    const listContainer = document.getElementById('employeeList');

    if (results.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد نتائج</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>الرتبة</th>
                    <th>رقم الذاتية</th>
                    <th>مكان العمل</th>
                    <th>الكتيبة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${results.map(emp => `
                    <tr>
                        <td>${emp.firstName} ${emp.lastName}</td>
                        <td>${emp.rank}</td>
                        <td>${emp.personalNumber}</td>
                        <td>${emp.workplace}</td>
                        <td>${emp.battalion}</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editEmployee('${emp.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteEmployee('${emp.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

function displayAnnualLeaveResults(results) {
    const listContainer = document.getElementById('annualLeaveList');

    if (results.length === 0) {
        listContainer.innerHTML = '<p class="text-center">لا توجد نتائج</p>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>الاسم الكامل</th>
                    <th>رقم الذاتية</th>
                    <th>نوع العطلة</th>
                    <th>السنة</th>
                    <th>تاريخ البداية</th>
                    <th>المدة</th>
                    <th>الأيام المتبقية</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${results.map(leave => `
                    <tr>
                        <td>${leave.firstName} ${leave.lastName}</td>
                        <td>${leave.personalNumber}</td>
                        <td>${leave.leaveType}</td>
                        <td>${leave.year}</td>
                        <td>${leave.startDate}</td>
                        <td>${leave.duration} يوم</td>
                        <td>${dataManager.calculateRemainingDays(leave.startDate, leave.duration)} يوم</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning btn-sm" onclick="editAnnualLeave('${leave.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteAnnualLeave('${leave.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    listContainer.innerHTML = tableHTML;
}

// Edit and Delete functions
function editEmployee(id) {
    const employee = dataManager.getById('employees', id);
    if (employee) {
        currentEditId = id;
        fillEmployeeForm(employee);
    }
}

function deleteEmployee(id) {
    if (confirm('هل أنت متأكد من حذف بيانات هذا الموظف؟')) {
        dataManager.delete('employees', id);
        loadEmployeeList();
        alert('تم حذف بيانات الموظف بنجاح');
    }
}

function editSickLeave(id) {
    const sickLeave = dataManager.getById('sickLeave', id);
    if (sickLeave) {
        currentEditId = id;
        fillSickLeaveForm(sickLeave);
    }
}

function deleteSickLeave(id) {
    if (confirm('هل أنت متأكد من حذف بيانات هذه العطلة المرضية؟')) {
        dataManager.delete('sickLeave', id);
        loadSickLeaveList();
        alert('تم حذف بيانات العطلة المرضية بنجاح');
    }
}

function editAnnualLeave(id) {
    const annualLeave = dataManager.getById('annualLeave', id);
    if (annualLeave) {
        currentEditId = id;
        fillAnnualLeaveForm(annualLeave);
    }
}

function deleteAnnualLeave(id) {
    if (confirm('هل أنت متأكد من حذف بيانات هذه العطلة؟')) {
        dataManager.delete('annualLeave', id);
        loadAnnualLeaveList();
        alert('تم حذف بيانات العطلة بنجاح');
    }
}

// Form filling functions
function fillEmployeeForm(employee) {
    const form = document.getElementById('employeeForm');
    Object.keys(employee).forEach(key => {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = employee[key];
        }
    });

    if (employee.photo) {
        const img = document.getElementById('employeePhoto');
        const placeholder = document.getElementById('photoPlaceholder');
        img.src = employee.photo;
        img.style.display = 'block';
        placeholder.style.display = 'none';
    }
}

function fillSickLeaveForm(sickLeave) {
    const form = document.getElementById('sickLeaveForm');
    Object.keys(sickLeave).forEach(key => {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = sickLeave[key];
        }
    });
}

function fillAnnualLeaveForm(annualLeave) {
    const form = document.getElementById('annualLeaveForm');
    Object.keys(annualLeave).forEach(key => {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = annualLeave[key];
        }
    });
}
