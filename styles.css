/* Neumorphic Design System */
:root {
    --primary-color: #6c5ce7;
    --secondary-color: #a29bfe;
    --accent-color: #fd79a8;
    --success-color: #00b894;
    --warning-color: #fdcb6e;
    --danger-color: #e17055;
    --dark-color: #2d3436;
    --light-color: #ddd6fe;
    --bg-color: #e3f2fd;
    --surface-color: #f8f9fa;
    --text-color: #2d3436;
    --shadow-light: #ffffff;
    --shadow-dark: #babecc;
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--text-color);
}

/* Login Page Styles */
.login-body {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: 
        20px 20px 60px #bebebe,
        -20px -20px 60px #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block;
}

.login-header h2 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-weight: 700;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container i {
    position: absolute;
    right: 15px;
    color: var(--primary-color);
    z-index: 2;
}

.input-container input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: none;
    border-radius: 15px;
    background: #f0f0f3;
    box-shadow: 
        inset 8px 8px 16px #d1d1d4,
        inset -8px -8px 16px #ffffff;
    font-size: 1rem;
    transition: var(--transition);
    outline: none;
}

.input-container input:focus {
    box-shadow: 
        inset 4px 4px 8px #d1d1d4,
        inset -4px -4px 8px #ffffff;
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    z-index: 2;
}

.login-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        8px 8px 16px #d1d1d4,
        -8px -8px 16px #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        12px 12px 20px #d1d1d4,
        -12px -12px 20px #ffffff;
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: 
        4px 4px 8px #d1d1d4,
        -4px -4px 8px #ffffff;
}

.error-message {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: white;
    padding: 12px;
    border-radius: 10px;
    text-align: center;
    margin-top: 15px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-footer {
    text-align: center;
    margin-top: 30px;
    color: #666;
    font-size: 0.8rem;
}

/* Main Application Styles */
.main-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    box-shadow: 
        0 8px 32px rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.18);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo h1 {
    color: var(--dark-color);
    font-weight: 700;
    margin: 0;
}

.datetime {
    text-align: center;
    color: var(--dark-color);
}

.datetime .date {
    font-size: 1.2rem;
    font-weight: 600;
}

.datetime .time {
    font-size: 1rem;
    color: #666;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-info span {
    font-weight: 600;
    color: var(--dark-color);
    margin-left: 10px;
}

.logout-btn {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 
        6px 6px 12px rgba(0,0,0,0.1),
        -6px -6px 12px rgba(255,255,255,0.7);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        8px 8px 16px rgba(0,0,0,0.15),
        -8px -8px 16px rgba(255,255,255,0.8);
}

/* Dashboard Grid */
.dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    padding: 25px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        12px 12px 24px rgba(163, 177, 198, 0.6),
        -12px -12px 24px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        16px 16px 32px rgba(163, 177, 198, 0.8),
        -16px -16px 32px rgba(255, 255, 255, 0.9);
}

.card-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.card-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        padding: 30px 20px;
    }
}

/* Modal Styles */
.modal-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    border: none;
    box-shadow:
        20px 20px 60px rgba(163, 177, 198, 0.6),
        -20px -20px 60px rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
    padding: 20px 25px;
}

.modal-header .modal-title {
    font-weight: 700;
    font-size: 1.3rem;
}

.modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 20px 25px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-control, .form-select {
    border: none;
    border-radius: 12px;
    background: #f8f9fa;
    box-shadow:
        inset 6px 6px 12px rgba(163, 177, 198, 0.4),
        inset -6px -6px 12px rgba(255, 255, 255, 0.8);
    padding: 12px 15px;
    font-size: 0.9rem;
    transition: var(--transition);
    color: var(--dark-color);
}

.form-control:focus, .form-select:focus {
    box-shadow:
        inset 3px 3px 6px rgba(163, 177, 198, 0.6),
        inset -3px -3px 6px rgba(255, 255, 255, 0.9);
    outline: none;
    border: none;
}

.form-control:read-only {
    background: #e9ecef;
    color: #6c757d;
}

/* Photo Upload Styles */
.photo-upload-section {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 20px;
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.4),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.photo-upload-area {
    width: 200px;
    height: 250px;
    border: 2px dashed var(--primary-color);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    background: rgba(108, 92, 231, 0.05);
    margin: 0 auto;
}

.photo-upload-area:hover {
    border-color: var(--secondary-color);
    background: rgba(108, 92, 231, 0.1);
    transform: translateY(-2px);
}

.photo-upload-area img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 13px;
}

#photoPlaceholder {
    text-align: center;
    color: var(--primary-color);
}

#photoPlaceholder i {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

#photoPlaceholder p {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Button Styles */
.btn {
    border-radius: 12px;
    padding: 10px 20px;
    font-weight: 600;
    border: none;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow:
        6px 6px 12px rgba(163, 177, 198, 0.4),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.5),
        -8px -8px 16px rgba(255, 255, 255, 0.9);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    box-shadow:
        6px 6px 12px rgba(163, 177, 198, 0.4),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #55efc4);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffeaa7);
    color: var(--dark-color);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #ff7675);
    color: white;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Data List Styles */
.data-list {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.4),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.data-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(108, 92, 231, 0.1);
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box input {
    width: 250px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.data-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background: rgba(108, 92, 231, 0.05);
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Statistics Styles */
.statistics-summary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.stat-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.4),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    border-left: 4px solid var(--primary-color);
}

.stat-card h6 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(108, 92, 231, 0.1);
    padding-bottom: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: var(--dark-color);
}

.stat-value {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* CSV Navigation */
.csv-navigation .btn-group .btn {
    border-radius: 0;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    transition: var(--transition);
}

.csv-navigation .btn-group .btn:first-child {
    border-radius: 12px 0 0 12px;
}

.csv-navigation .btn-group .btn:last-child {
    border-radius: 0 12px 12px 0;
}

.csv-navigation .btn-group .btn.active,
.csv-navigation .btn-group .btn:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

/* CSV Data Container */
#csvDataContainer {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 10px;
    padding: 15px;
    background: rgba(248, 249, 250, 0.8);
}

/* Enhanced Form Controls */
.form-check {
    margin-bottom: 10px;
}

.form-check-input {
    border-radius: 6px;
    border: 2px solid var(--primary-color);
    width: 1.2em;
    height: 1.2em;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-right: 8px;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, var(--success-color), #55efc4);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, var(--danger-color), #ff7675);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffeaa7);
    color: var(--dark-color);
}

.alert-info {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

/* Print Styles */
@media print {
    .modal-header,
    .modal-footer,
    .btn,
    .action-buttons {
        display: none !important;
    }

    .modal-body {
        padding: 0;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }
}

/* Mobile Responsive Enhancements */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }

    .action-buttons .btn {
        width: 100%;
        font-size: 0.7rem;
        padding: 3px 6px;
    }

    .csv-navigation .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .csv-navigation .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 5px;
    }
}
