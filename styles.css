/* Neumorphic Design System */
:root {
    --primary-color: #6c5ce7;
    --secondary-color: #a29bfe;
    --accent-color: #fd79a8;
    --success-color: #00b894;
    --warning-color: #fdcb6e;
    --danger-color: #e17055;
    --dark-color: #2d3436;
    --light-color: #ddd6fe;
    --bg-color: #e3f2fd;
    --surface-color: #f8f9fa;
    --text-color: #2d3436;
    --shadow-light: #ffffff;
    --shadow-dark: #babecc;
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--text-color);
}

/* Login Page Styles */
.login-body {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: 
        20px 20px 60px #bebebe,
        -20px -20px 60px #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block;
}

.login-header h2 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-weight: 700;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container i {
    position: absolute;
    right: 15px;
    color: var(--primary-color);
    z-index: 2;
}

.input-container input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: none;
    border-radius: 15px;
    background: #f0f0f3;
    box-shadow: 
        inset 8px 8px 16px #d1d1d4,
        inset -8px -8px 16px #ffffff;
    font-size: 1rem;
    transition: var(--transition);
    outline: none;
}

.input-container input:focus {
    box-shadow: 
        inset 4px 4px 8px #d1d1d4,
        inset -4px -4px 8px #ffffff;
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    z-index: 2;
}

.login-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        8px 8px 16px #d1d1d4,
        -8px -8px 16px #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        12px 12px 20px #d1d1d4,
        -12px -12px 20px #ffffff;
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: 
        4px 4px 8px #d1d1d4,
        -4px -4px 8px #ffffff;
}

.error-message {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: white;
    padding: 12px;
    border-radius: 10px;
    text-align: center;
    margin-top: 15px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-footer {
    text-align: center;
    margin-top: 30px;
    color: #666;
    font-size: 0.8rem;
}

/* Main Application Styles */
.main-body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    box-shadow: 
        0 8px 32px rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.18);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo h1 {
    color: var(--dark-color);
    font-weight: 700;
    margin: 0;
}

.datetime {
    text-align: center;
    color: var(--dark-color);
}

.datetime .date {
    font-size: 1.2rem;
    font-weight: 600;
}

.datetime .time {
    font-size: 1rem;
    color: #666;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 
        6px 6px 12px rgba(0,0,0,0.1),
        -6px -6px 12px rgba(255,255,255,0.7);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        8px 8px 16px rgba(0,0,0,0.15),
        -8px -8px 16px rgba(255,255,255,0.8);
}

/* Dashboard Grid */
.dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    padding: 25px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        12px 12px 24px rgba(163, 177, 198, 0.6),
        -12px -12px 24px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        16px 16px 32px rgba(163, 177, 198, 0.8),
        -16px -16px 32px rgba(255, 255, 255, 0.9);
}

.card-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.card-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        padding: 30px 20px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
