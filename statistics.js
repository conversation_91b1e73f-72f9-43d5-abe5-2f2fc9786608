// Statistics Manager for Employee Management System
class StatisticsManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
    }

    // Employee statistics by rank
    getEmployeesByRank() {
        const employees = this.dataManager.getAll('employees');
        const rankStats = {};

        employees.forEach(emp => {
            const rank = emp.rank || 'غير محدد';
            rankStats[rank] = (rankStats[rank] || 0) + 1;
        });

        return rankStats;
    }

    // Employee statistics by workplace
    getEmployeesByWorkplace() {
        const employees = this.dataManager.getAll('employees');
        const workplaceStats = {};

        employees.forEach(emp => {
            const workplace = emp.workplace || 'غير محدد';
            workplaceStats[workplace] = (workplaceStats[workplace] || 0) + 1;
        });

        return workplaceStats;
    }

    // Employee statistics by battalion
    getEmployeesByBattalion() {
        const employees = this.dataManager.getAll('employees');
        const battalionStats = {};

        employees.forEach(emp => {
            const battalion = emp.battalion || 'غير محدد';
            battalionStats[battalion] = (battalionStats[battalion] || 0) + 1;
        });

        return battalionStats;
    }

    // Penalties statistics by period
    getPenaltiesByPeriod(period = 'month') {
        const penalties = this.dataManager.getAll('penalties');
        const stats = {};

        penalties.forEach(penalty => {
            if (penalty.incidentDate) {
                const date = new Date(penalty.incidentDate);
                let key;

                switch (period) {
                    case 'month':
                        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        key = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        key = date.getFullYear().toString();
                        break;
                }

                stats[key] = (stats[key] || 0) + 1;
            }
        });

        return stats;
    }

    // Annual leave statistics by period
    getAnnualLeaveByPeriod(period = 'month') {
        const leaves = this.dataManager.getAll('annualLeave');
        const stats = {};

        leaves.forEach(leave => {
            if (leave.startDate) {
                const date = new Date(leave.startDate);
                let key;

                switch (period) {
                    case 'day':
                        key = date.toISOString().split('T')[0];
                        break;
                    case 'month':
                        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        key = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        key = date.getFullYear().toString();
                        break;
                }

                stats[key] = (stats[key] || 0) + 1;
            }
        });

        return stats;
    }

    // Sick leave statistics by type and period
    getSickLeaveByTypeAndPeriod(period = 'month') {
        const sickLeaves = this.dataManager.getAll('sickLeave');
        const stats = {};

        sickLeaves.forEach(leave => {
            if (leave.startDate && leave.leaveType) {
                const date = new Date(leave.startDate);
                let timeKey;

                switch (period) {
                    case 'month':
                        timeKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        timeKey = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        timeKey = date.getFullYear().toString();
                        break;
                }

                const key = `${leave.leaveType} - ${timeKey}`;
                stats[key] = (stats[key] || 0) + 1;
            }
        });

        return stats;
    }

    // Absence statistics by employee and period
    getAbsencesByEmployeeAndPeriod(period = 'month') {
        const absences = this.dataManager.getAll('absences');
        const employees = this.dataManager.getAll('employees');
        const stats = {};

        absences.forEach(absence => {
            if (absence.absenceDate && absence.employeeId) {
                const employee = employees.find(emp => emp.id === absence.employeeId);
                const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : 'غير معروف';

                const date = new Date(absence.absenceDate);
                let timeKey;

                switch (period) {
                    case 'day':
                        timeKey = date.toISOString().split('T')[0];
                        break;
                    case 'month':
                        timeKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        timeKey = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        timeKey = date.getFullYear().toString();
                        break;
                }

                const key = `${employeeName} - ${timeKey}`;
                stats[key] = (stats[key] || 0) + 1;
            }
        });

        return stats;
    }

    // Generate comprehensive report
    generateComprehensiveReport() {
        return {
            employees: {
                byRank: this.getEmployeesByRank(),
                byWorkplace: this.getEmployeesByWorkplace(),
                byBattalion: this.getEmployeesByBattalion()
            },
            penalties: {
                byMonth: this.getPenaltiesByPeriod('month'),
                byQuarter: this.getPenaltiesByPeriod('quarter'),
                byYear: this.getPenaltiesByPeriod('year')
            },
            annualLeave: {
                byDay: this.getAnnualLeaveByPeriod('day'),
                byMonth: this.getAnnualLeaveByPeriod('month'),
                byQuarter: this.getAnnualLeaveByPeriod('quarter'),
                byYear: this.getAnnualLeaveByPeriod('year')
            },
            sickLeave: {
                byMonth: this.getSickLeaveByTypeAndPeriod('month'),
                byQuarter: this.getSickLeaveByTypeAndPeriod('quarter'),
                byYear: this.getSickLeaveByTypeAndPeriod('year')
            },
            absences: {
                byDay: this.getAbsencesByEmployeeAndPeriod('day'),
                byMonth: this.getAbsencesByEmployeeAndPeriod('month'),
                byQuarter: this.getAbsencesByEmployeeAndPeriod('quarter'),
                byYear: this.getAbsencesByEmployeeAndPeriod('year')
            }
        };
    }
}

// Initialize statistics manager
const statisticsManager = new StatisticsManager(dataManager);
