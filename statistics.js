// Statistics Manager for Employee Management System
class StatisticsManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
    }

    // Employee statistics by rank
    getEmployeesByRank() {
        const employees = this.dataManager.getAll('employees');
        const rankStats = {};
        
        employees.forEach(emp => {
            const rank = emp.rank || 'غير محدد';
            rankStats[rank] = (rankStats[rank] || 0) + 1;
        });
        
        return rankStats;
    }

    // Employee statistics by workplace
    getEmployeesByWorkplace() {
        const employees = this.dataManager.getAll('employees');
        const workplaceStats = {};
        
        employees.forEach(emp => {
            const workplace = emp.workplace || 'غير محدد';
            workplaceStats[workplace] = (workplaceStats[workplace] || 0) + 1;
        });
        
        return workplaceStats;
    }

    // Employee statistics by battalion
    getEmployeesByBattalion() {
        const employees = this.dataManager.getAll('employees');
        const battalionStats = {};
        
        employees.forEach(emp => {
            const battalion = emp.battalion || 'غير محدد';
            battalionStats[battalion] = (battalionStats[battalion] || 0) + 1;
        });
        
        return battalionStats;
    }

    // Employee statistics by appointment date
    getEmployeesByAppointmentDate() {
        const employees = this.dataManager.getAll('employees');
        const yearStats = {};
        
        employees.forEach(emp => {
            if (emp.appointmentDate) {
                const year = new Date(emp.appointmentDate).getFullYear();
                yearStats[year] = (yearStats[year] || 0) + 1;
            }
        });
        
        return yearStats;
    }

    // Employee statistics by service years
    getEmployeesByServiceYears() {
        const employees = this.dataManager.getAll('employees');
        const serviceStats = {
            '0-5 سنوات': 0,
            '6-10 سنوات': 0,
            '11-15 سنة': 0,
            '16-20 سنة': 0,
            '21-25 سنة': 0,
            'أكثر من 25 سنة': 0
        };
        
        employees.forEach(emp => {
            if (emp.appointmentDate) {
                const serviceYears = this.dataManager.calculateServiceYears(emp.appointmentDate);
                if (serviceYears <= 5) serviceStats['0-5 سنوات']++;
                else if (serviceYears <= 10) serviceStats['6-10 سنوات']++;
                else if (serviceYears <= 15) serviceStats['11-15 سنة']++;
                else if (serviceYears <= 20) serviceStats['16-20 سنة']++;
                else if (serviceYears <= 25) serviceStats['21-25 سنة']++;
                else serviceStats['أكثر من 25 سنة']++;
            }
        });
        
        return serviceStats;
    }

    // Penalties statistics by period
    getPenaltiesByPeriod(period = 'month') {
        const penalties = this.dataManager.getAll('penalties');
        const stats = {};
        
        penalties.forEach(penalty => {
            if (penalty.incidentDate) {
                const date = new Date(penalty.incidentDate);
                let key;
                
                switch (period) {
                    case 'month':
                        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        key = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        key = date.getFullYear().toString();
                        break;
                    default:
                        key = date.toISOString().split('T')[0];
                }
                
                stats[key] = (stats[key] || 0) + 1;
            }
        });
        
        return stats;
    }

    // Annual leave statistics by period
    getAnnualLeaveByPeriod(period = 'month') {
        const leaves = this.dataManager.getAll('annualLeave');
        const stats = {};
        
        leaves.forEach(leave => {
            if (leave.startDate) {
                const date = new Date(leave.startDate);
                let key;
                
                switch (period) {
                    case 'day':
                        key = date.toISOString().split('T')[0];
                        break;
                    case 'month':
                        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        key = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        key = date.getFullYear().toString();
                        break;
                    default:
                        key = date.toISOString().split('T')[0];
                }
                
                stats[key] = (stats[key] || 0) + 1;
            }
        });
        
        return stats;
    }

    // Sick leave statistics by type and period
    getSickLeaveByTypeAndPeriod(period = 'month') {
        const sickLeaves = this.dataManager.getAll('sickLeave');
        const stats = {};
        
        sickLeaves.forEach(leave => {
            if (leave.startDate && leave.leaveType) {
                const date = new Date(leave.startDate);
                let timeKey;
                
                switch (period) {
                    case 'month':
                        timeKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        timeKey = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        timeKey = date.getFullYear().toString();
                        break;
                    default:
                        timeKey = date.toISOString().split('T')[0];
                }
                
                const key = `${leave.leaveType} - ${timeKey}`;
                stats[key] = (stats[key] || 0) + 1;
            }
        });
        
        return stats;
    }

    // Absence statistics by employee and period
    getAbsencesByEmployeeAndPeriod(period = 'month') {
        const absences = this.dataManager.getAll('absences');
        const employees = this.dataManager.getAll('employees');
        const stats = {};
        
        absences.forEach(absence => {
            if (absence.absenceDate && absence.employeeId) {
                const employee = employees.find(emp => emp.id === absence.employeeId);
                const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : 'غير معروف';
                
                const date = new Date(absence.absenceDate);
                let timeKey;
                
                switch (period) {
                    case 'day':
                        timeKey = date.toISOString().split('T')[0];
                        break;
                    case 'month':
                        timeKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        timeKey = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        timeKey = date.getFullYear().toString();
                        break;
                    default:
                        timeKey = date.toISOString().split('T')[0];
                }
                
                const key = `${employeeName} - ${timeKey}`;
                stats[key] = (stats[key] || 0) + 1;
            }
        });
        
        return stats;
    }

    // Vacation statistics by period
    getVacationsByPeriod(period = 'month') {
        const vacations = this.dataManager.getAll('vacations');
        const stats = {};
        
        vacations.forEach(vacation => {
            if (vacation.startDate) {
                const date = new Date(vacation.startDate);
                let key;
                
                switch (period) {
                    case 'day':
                        key = date.toISOString().split('T')[0];
                        break;
                    case 'month':
                        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        break;
                    case 'quarter':
                        const quarter = Math.ceil((date.getMonth() + 1) / 3);
                        key = `${date.getFullYear()}-Q${quarter}`;
                        break;
                    case 'year':
                        key = date.getFullYear().toString();
                        break;
                    default:
                        key = date.toISOString().split('T')[0];
                }
                
                stats[key] = (stats[key] || 0) + 1;
            }
        });
        
        return stats;
    }

    // Generate comprehensive report
    generateComprehensiveReport() {
        return {
            employees: {
                byRank: this.getEmployeesByRank(),
                byWorkplace: this.getEmployeesByWorkplace(),
                byBattalion: this.getEmployeesByBattalion(),
                byAppointmentDate: this.getEmployeesByAppointmentDate(),
                byServiceYears: this.getEmployeesByServiceYears()
            },
            penalties: {
                byMonth: this.getPenaltiesByPeriod('month'),
                byQuarter: this.getPenaltiesByPeriod('quarter'),
                byYear: this.getPenaltiesByPeriod('year')
            },
            annualLeave: {
                byDay: this.getAnnualLeaveByPeriod('day'),
                byMonth: this.getAnnualLeaveByPeriod('month'),
                byQuarter: this.getAnnualLeaveByPeriod('quarter'),
                byYear: this.getAnnualLeaveByPeriod('year')
            },
            sickLeave: {
                byMonth: this.getSickLeaveByTypeAndPeriod('month'),
                byQuarter: this.getSickLeaveByTypeAndPeriod('quarter'),
                byYear: this.getSickLeaveByTypeAndPeriod('year')
            },
            absences: {
                byDay: this.getAbsencesByEmployeeAndPeriod('day'),
                byMonth: this.getAbsencesByEmployeeAndPeriod('month'),
                byQuarter: this.getAbsencesByEmployeeAndPeriod('quarter'),
                byYear: this.getAbsencesByEmployeeAndPeriod('year')
            },
            vacations: {
                byDay: this.getVacationsByPeriod('day'),
                byMonth: this.getVacationsByPeriod('month'),
                byQuarter: this.getVacationsByPeriod('quarter'),
                byYear: this.getVacationsByPeriod('year')
            }
        };
    }

    // Export statistics to CSV
    exportStatisticsToCSV(reportType) {
        const report = this.generateComprehensiveReport();
        const data = report[reportType];
        
        if (!data) {
            alert('نوع التقرير غير صحيح');
            return;
        }

        const csvData = [];
        Object.keys(data).forEach(category => {
            Object.keys(data[category]).forEach(key => {
                csvData.push({
                    category: category,
                    item: key,
                    count: data[category][key]
                });
            });
        });

        this.dataManager.exportToCSV('statistics', `statistics_${reportType}_${new Date().toISOString().split('T')[0]}.csv`);
    }
}

// Initialize statistics manager
const statisticsManager = new StatisticsManager(dataManager);
