// Advanced Dashboard Functions
class AdvancedDashboard {
    constructor() {
        this.charts = {};
        this.alerts = [];
        this.performanceMetrics = {};
    }

    // Initialize dashboard
    initializeDashboard() {
        this.loadMetrics();
        this.createCharts();
        this.loadAlerts();
        this.loadPerformanceIndicators();
        this.startRealTimeUpdates();
    }

    // Load key metrics
    loadMetrics() {
        const stats = dataManager.getStatistics();
        const employees = dataManager.getAll('employees');
        const sickLeaves = dataManager.getAll('sickLeave');
        const annualLeaves = dataManager.getAll('annualLeave');
        const absences = dataManager.getAll('absences');

        // Total employees
        document.getElementById('totalEmployees').textContent = stats.totalEmployees;

        // Active leaves
        const activeLeaves = this.calculateActiveLeaves();
        document.getElementById('activeLeaves').textContent = activeLeaves;

        // Pending actions
        const pendingActions = this.calculatePendingActions();
        document.getElementById('pendingActions').textContent = pendingActions;

        // Attendance rate
        const attendanceRate = this.calculateAttendanceRate();
        document.getElementById('attendanceRate').textContent = attendanceRate + '%';

        // Calculate changes (mock data for demo)
        this.updateMetricChanges();
    }

    // Calculate active leaves
    calculateActiveLeaves() {
        const sickLeaves = dataManager.getAll('sickLeave');
        const annualLeaves = dataManager.getAll('annualLeave');
        const vacations = dataManager.getAll('vacations');

        let activeCount = 0;
        const today = new Date();

        [...sickLeaves, ...annualLeaves, ...vacations].forEach(leave => {
            if (leave.startDate && leave.duration) {
                const remaining = dataManager.calculateRemainingDays(leave.startDate, leave.duration);
                if (remaining > 0) activeCount++;
            }
        });

        return activeCount;
    }

    // Calculate pending actions
    calculatePendingActions() {
        const penalties = dataManager.getAll('penalties');
        const sickLeaves = dataManager.getAll('sickLeave');
        
        let pendingCount = 0;

        // Check for penalties without notification
        penalties.forEach(penalty => {
            if (!penalty.notificationDate) pendingCount++;
        });

        // Check for long sick leaves
        sickLeaves.forEach(leave => {
            if (leave.duration > 30) pendingCount++;
        });

        return pendingCount;
    }

    // Calculate attendance rate
    calculateAttendanceRate() {
        const employees = dataManager.getAll('employees');
        const absences = dataManager.getAll('absences');
        
        if (employees.length === 0) return 100;

        const totalWorkDays = employees.length * 30; // Assuming 30 work days per month
        const totalAbsences = absences.length;
        
        const attendanceRate = ((totalWorkDays - totalAbsences) / totalWorkDays) * 100;
        return Math.round(attendanceRate);
    }

    // Update metric changes
    updateMetricChanges() {
        // Mock data for demonstration
        document.getElementById('employeeChange').textContent = '****%';
        document.getElementById('leaveChange').textContent = '3';
        document.getElementById('actionChange').textContent = '2';
        document.getElementById('attendanceChange').textContent = '****%';
    }

    // Create charts
    createCharts() {
        this.createBattalionChart();
        this.createLeaveChart();
    }

    // Create battalion distribution chart
    createBattalionChart() {
        const ctx = document.getElementById('battalionChart').getContext('2d');
        const battalionStats = statisticsManager.getEmployeesByBattalion();

        this.charts.battalion = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(battalionStats),
                datasets: [{
                    label: 'عدد الموظفين',
                    data: Object.values(battalionStats),
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(118, 75, 162, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 205, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Create leave distribution chart
    createLeaveChart() {
        const ctx = document.getElementById('leaveChart').getContext('2d');
        const sickLeaves = dataManager.getAll('sickLeave').length;
        const annualLeaves = dataManager.getAll('annualLeave').length;
        const vacations = dataManager.getAll('vacations').length;

        this.charts.leave = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['العطل المرضية', 'العطل السنوية', 'الإجازات'],
                datasets: [{
                    data: [sickLeaves, annualLeaves, vacations],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)'
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(46, 204, 113, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Load alerts and notifications
    loadAlerts() {
        const alerts = this.generateAlerts();
        const container = document.getElementById('alertsContainer');

        if (alerts.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">لا توجد تنبيهات جديدة</p>';
            return;
        }

        const alertsHTML = alerts.map(alert => `
            <div class="alert-item alert-${alert.type}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">
                            <i class="bi bi-${alert.icon}"></i>
                            ${alert.title}
                        </h6>
                        <p class="mb-1">${alert.message}</p>
                        <small class="text-muted">${alert.time}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" onclick="dismissAlert('${alert.id}')">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
        `).join('');

        container.innerHTML = alertsHTML;
    }

    // Generate smart alerts
    generateAlerts() {
        const alerts = [];
        const today = new Date();

        // Check for expiring leaves
        const sickLeaves = dataManager.getAll('sickLeave');
        sickLeaves.forEach(leave => {
            const remaining = dataManager.calculateRemainingDays(leave.startDate, leave.duration);
            if (remaining > 0 && remaining <= 3) {
                alerts.push({
                    id: `leave_${leave.id}`,
                    type: 'warning',
                    icon: 'calendar-x',
                    title: 'عطلة مرضية تنتهي قريباً',
                    message: `عطلة ${leave.firstName} ${leave.lastName} تنتهي خلال ${remaining} أيام`,
                    time: 'منذ دقائق'
                });
            }
        });

        // Check for pending penalties
        const penalties = dataManager.getAll('penalties');
        penalties.forEach(penalty => {
            if (!penalty.notificationDate) {
                alerts.push({
                    id: `penalty_${penalty.id}`,
                    type: 'urgent',
                    icon: 'exclamation-triangle',
                    title: 'عقوبة تحتاج تبليغ',
                    message: `عقوبة ${penalty.firstName} ${penalty.lastName} تحتاج إلى إثبات تبليغ`,
                    time: 'منذ ساعة'
                });
            }
        });

        // Check for long absences
        const absences = dataManager.getAll('absences');
        const employeeAbsences = {};
        absences.forEach(absence => {
            if (!employeeAbsences[absence.personalNumber]) {
                employeeAbsences[absence.personalNumber] = 0;
            }
            employeeAbsences[absence.personalNumber]++;
        });

        Object.keys(employeeAbsences).forEach(personalNumber => {
            if (employeeAbsences[personalNumber] > 5) {
                const employee = dataManager.search('employees', 'personalNumber', personalNumber)[0];
                if (employee) {
                    alerts.push({
                        id: `absence_${personalNumber}`,
                        type: 'info',
                        icon: 'person-x',
                        title: 'غيابات متكررة',
                        message: `${employee.firstName} ${employee.lastName} لديه ${employeeAbsences[personalNumber]} غيابات`,
                        time: 'اليوم'
                    });
                }
            }
        });

        return alerts.slice(0, 5); // Show only first 5 alerts
    }

    // Load performance indicators
    loadPerformanceIndicators() {
        const indicators = [
            { name: 'معدل الحضور الشهري', value: 95, target: 98 },
            { name: 'معدل إنجاز التقييمات', value: 78, target: 90 },
            { name: 'معدل التدريب السنوي', value: 65, target: 80 },
            { name: 'معدل رضا الموظفين', value: 88, target: 85 },
            { name: 'معدل الاحتفاظ بالموظفين', value: 92, target: 90 }
        ];

        const container = document.getElementById('performanceIndicators');
        const indicatorsHTML = indicators.map(indicator => {
            const percentage = (indicator.value / indicator.target) * 100;
            const status = percentage >= 100 ? 'success' : percentage >= 80 ? 'warning' : 'danger';
            
            return `
                <div class="performance-indicator">
                    <div>
                        <strong>${indicator.name}</strong>
                        <br>
                        <small class="text-muted">${indicator.value}% من ${indicator.target}%</small>
                    </div>
                    <div class="performance-bar">
                        <div class="performance-fill" style="width: ${Math.min(percentage, 100)}%"></div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = indicatorsHTML;
    }

    // Start real-time updates
    startRealTimeUpdates() {
        setInterval(() => {
            this.loadMetrics();
            this.loadAlerts();
        }, 60000); // Update every minute
    }
}

// Quick action functions
function quickAddEmployee() {
    window.location.href = 'main.html';
    setTimeout(() => {
        openModal('employeeModal');
    }, 1000);
}

function quickBackup() {
    const allData = {
        employees: dataManager.getAll('employees'),
        sickLeave: dataManager.getAll('sickLeave'),
        annualLeave: dataManager.getAll('annualLeave'),
        penalties: dataManager.getAll('penalties'),
        absences: dataManager.getAll('absences'),
        training: dataManager.getAll('training'),
        vacations: dataManager.getAll('vacations'),
        evaluations: dataManager.getAll('evaluations'),
        timestamp: new Date().toISOString()
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    alert('تم إنشاء النسخة الاحتياطية بنجاح');
}

function generateReport() {
    // This will be implemented with PDF generation
    alert('سيتم إضافة ميزة تقارير PDF قريباً');
}

function bulkImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // Import all data types
                    Object.keys(data).forEach(dataType => {
                        if (dataType !== 'timestamp' && Array.isArray(data[dataType])) {
                            localStorage.setItem(dataType, JSON.stringify(data[dataType]));
                        }
                    });
                    
                    alert('تم استيراد البيانات بنجاح');
                    location.reload();
                } catch (error) {
                    alert('خطأ في تنسيق الملف');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function dismissAlert(alertId) {
    const alertElement = document.querySelector(`[onclick="dismissAlert('${alertId}')"]`).closest('.alert-item');
    alertElement.style.animation = 'fadeOut 0.3s ease';
    setTimeout(() => {
        alertElement.remove();
    }, 300);
}

// Initialize dashboard
let advancedDashboard;

function initializeDashboard() {
    advancedDashboard = new AdvancedDashboard();
    advancedDashboard.initializeDashboard();
}

// CSS animation for alert dismissal
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(100%); }
    }
`;
document.head.appendChild(style);
