// Data Manager for Employee Management System
class DataManager {
    constructor() {
        this.initializeData();
    }

    // Initialize data structure
    initializeData() {
        const dataTypes = [
            'employees',
            'sickLeave',
            'annualLeave',
            'penalties',
            'absences',
            'training',
            'vacations',
            'evaluations'
        ];

        dataTypes.forEach(type => {
            if (!localStorage.getItem(type)) {
                localStorage.setItem(type, JSON.stringify([]));
            }
        });
    }

    // Generic CRUD operations
    create(type, data) {
        const items = this.getAll(type);
        data.id = this.generateId();
        data.createdAt = new Date().toISOString();
        data.updatedAt = new Date().toISOString();
        items.push(data);
        localStorage.setItem(type, JSON.stringify(items));
        return data;
    }

    getAll(type) {
        return JSON.parse(localStorage.getItem(type) || '[]');
    }

    getById(type, id) {
        const items = this.getAll(type);
        return items.find(item => item.id === id);
    }

    getByEmployeeId(type, employeeId) {
        const items = this.getAll(type);
        return items.filter(item => item.employeeId === employeeId);
    }

    update(type, id, data) {
        const items = this.getAll(type);
        const index = items.findIndex(item => item.id === id);
        if (index !== -1) {
            data.updatedAt = new Date().toISOString();
            items[index] = { ...items[index], ...data };
            localStorage.setItem(type, JSON.stringify(items));
            return items[index];
        }
        return null;
    }

    delete(type, id) {
        const items = this.getAll(type);
        const filteredItems = items.filter(item => item.id !== id);
        localStorage.setItem(type, JSON.stringify(filteredItems));
        return filteredItems.length < items.length;
    }

    // Search functionality
    search(type, field, value) {
        const items = this.getAll(type);
        return items.filter(item => 
            item[field] && item[field].toString().toLowerCase().includes(value.toLowerCase())
        );
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Calculate age from birth date
    calculateAge(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    }

    // Calculate service years
    calculateServiceYears(appointmentDate) {
        const today = new Date();
        const appointment = new Date(appointmentDate);
        let years = today.getFullYear() - appointment.getFullYear();
        const monthDiff = today.getMonth() - appointment.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < appointment.getDate())) {
            years--;
        }
        
        return years;
    }

    // Calculate remaining days for leaves
    calculateRemainingDays(startDate, duration) {
        const start = new Date(startDate);
        const today = new Date();
        const endDate = new Date(start);
        endDate.setDate(start.getDate() + parseInt(duration));
        
        if (today > endDate) {
            return 0;
        }
        
        const diffTime = endDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    }

    // Export to CSV
    exportToCSV(type, filename) {
        const data = this.getAll(type);
        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header] || '';
                    return `"${value.toString().replace(/"/g, '""')}"`;
                }).join(',')
            )
        ].join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename || `${type}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Import from CSV
    importFromCSV(type, file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n');
                    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
                    const data = [];

                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
                            const obj = {};
                            headers.forEach((header, index) => {
                                obj[header] = values[index] || '';
                            });
                            data.push(obj);
                        }
                    }

                    localStorage.setItem(type, JSON.stringify(data));
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.readAsText(file);
        });
    }

    // Get statistics
    getStatistics() {
        const employees = this.getAll('employees');
        const sickLeave = this.getAll('sickLeave');
        const annualLeave = this.getAll('annualLeave');
        const penalties = this.getAll('penalties');
        const absences = this.getAll('absences');
        const training = this.getAll('training');
        const vacations = this.getAll('vacations');
        const evaluations = this.getAll('evaluations');

        return {
            totalEmployees: employees.length,
            activeSickLeave: sickLeave.filter(leave => this.calculateRemainingDays(leave.startDate, leave.duration) > 0).length,
            activeAnnualLeave: annualLeave.filter(leave => this.calculateRemainingDays(leave.startDate, leave.duration) > 0).length,
            totalPenalties: penalties.length,
            totalAbsences: absences.length,
            activeTraining: training.length,
            activeVacations: vacations.filter(vacation => this.calculateRemainingDays(vacation.startDate, vacation.duration) > 0).length,
            totalEvaluations: evaluations.length
        };
    }

    // Generate evaluation comment based on score
    generateEvaluationComment(score, year) {
        const comments = {
            4: [
                `الأداء المهني للموظف في سنة ${year} كان ضعيفاً ويحتاج إلى تحسين كبير`,
                `الانضباط في العمل غير مرضي ويتطلب متابعة مستمرة`,
                `المردود المهني للموظف أقل من المتوقع ويحتاج إلى تطوير`,
                `يُنصح بوضع خطة تحسين شاملة للأداء`
            ],
            5: [
                `الأداء المهني للموظف في سنة ${year} كان مقبولاً مع وجود مجال للتحسين`,
                `الانضباط في العمل جيد ولكن يحتاج إلى مزيد من الالتزام`,
                `المردود المهني للموظف متوسط ويمكن تطويره`,
                `يُنصح بالمشاركة في دورات تدريبية لتحسين الأداء`
            ],
            6: [
                `الأداء المهني للموظف في سنة ${year} كان جيداً ومرضياً`,
                `الانضباط في العمل ممتاز ويُحتذى به`,
                `المردود المهني للموظف جيد ويساهم في تحقيق الأهداف`,
                `يُنصح بالاستمرار على هذا المستوى مع التطوير المستمر`
            ],
            7: [
                `الأداء المهني للموظف في سنة ${year} كان ممتازاً ومتميزاً`,
                `الانضباط في العمل مثالي ويُعتبر قدوة للآخرين`,
                `المردود المهني للموظف ممتاز ويفوق التوقعات`,
                `يُنصح بتكليفه بمهام إضافية وقيادية`
            ]
        };

        return comments[score] || comments[5];
    }
}

// Initialize data manager
const dataManager = new DataManager();
