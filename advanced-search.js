// Advanced Search Engine
class AdvancedSearchEngine {
    constructor() {
        this.searchIndex = {};
        this.searchHistory = [];
        this.maxHistoryItems = 50;
        this.buildSearchIndex();
    }

    // Build search index for faster searching
    buildSearchIndex() {
        const dataTypes = [
            'employees', 'sickLeave', 'annualLeave', 'penalties', 
            'absences', 'training', 'vacations', 'evaluations'
        ];

        this.searchIndex = {};

        dataTypes.forEach(type => {
            const data = dataManager.getAll(type);
            this.searchIndex[type] = {};

            data.forEach(item => {
                // Index all searchable fields
                Object.keys(item).forEach(field => {
                    if (typeof item[field] === 'string' || typeof item[field] === 'number') {
                        const value = item[field].toString().toLowerCase();
                        
                        if (!this.searchIndex[type][field]) {
                            this.searchIndex[type][field] = {};
                        }
                        
                        if (!this.searchIndex[type][field][value]) {
                            this.searchIndex[type][field][value] = [];
                        }
                        
                        this.searchIndex[type][field][value].push(item.id);
                    }
                });
            });
        });
    }

    // Advanced search with multiple criteria
    advancedSearch(criteria) {
        const results = {
            employees: [],
            sickLeave: [],
            annualLeave: [],
            penalties: [],
            absences: [],
            training: [],
            vacations: [],
            evaluations: []
        };

        // Add to search history
        this.addToSearchHistory(criteria);

        Object.keys(criteria).forEach(dataType => {
            if (criteria[dataType] && Object.keys(criteria[dataType]).length > 0) {
                results[dataType] = this.searchInDataType(dataType, criteria[dataType]);
            }
        });

        return results;
    }

    // Search in specific data type
    searchInDataType(dataType, searchCriteria) {
        const allData = dataManager.getAll(dataType);
        
        return allData.filter(item => {
            return Object.keys(searchCriteria).every(field => {
                const searchValue = searchCriteria[field];
                const itemValue = item[field];

                if (!searchValue || searchValue === '') return true;

                // Handle different search types
                if (searchValue.type === 'exact') {
                    return itemValue === searchValue.value;
                } else if (searchValue.type === 'range') {
                    const numValue = parseFloat(itemValue);
                    return numValue >= searchValue.min && numValue <= searchValue.max;
                } else if (searchValue.type === 'date_range') {
                    const itemDate = new Date(itemValue);
                    const startDate = new Date(searchValue.start);
                    const endDate = new Date(searchValue.end);
                    return itemDate >= startDate && itemDate <= endDate;
                } else if (searchValue.type === 'contains') {
                    return itemValue && itemValue.toString().toLowerCase().includes(searchValue.value.toLowerCase());
                } else {
                    // Default: contains search
                    return itemValue && itemValue.toString().toLowerCase().includes(searchValue.toString().toLowerCase());
                }
            });
        });
    }

    // Smart search with suggestions
    smartSearch(query) {
        const suggestions = [];
        const results = {};

        // Normalize query
        const normalizedQuery = query.toLowerCase().trim();

        if (normalizedQuery.length < 2) {
            return { results: {}, suggestions: [] };
        }

        // Search across all data types
        const dataTypes = ['employees', 'sickLeave', 'annualLeave', 'penalties', 'absences', 'training', 'vacations', 'evaluations'];

        dataTypes.forEach(dataType => {
            const data = dataManager.getAll(dataType);
            const matches = [];

            data.forEach(item => {
                let relevanceScore = 0;
                let matchedFields = [];

                Object.keys(item).forEach(field => {
                    if (typeof item[field] === 'string') {
                        const fieldValue = item[field].toLowerCase();
                        
                        // Exact match
                        if (fieldValue === normalizedQuery) {
                            relevanceScore += 100;
                            matchedFields.push({ field, type: 'exact' });
                        }
                        // Starts with
                        else if (fieldValue.startsWith(normalizedQuery)) {
                            relevanceScore += 50;
                            matchedFields.push({ field, type: 'starts_with' });
                        }
                        // Contains
                        else if (fieldValue.includes(normalizedQuery)) {
                            relevanceScore += 25;
                            matchedFields.push({ field, type: 'contains' });
                        }
                        // Fuzzy match
                        else if (this.fuzzyMatch(fieldValue, normalizedQuery)) {
                            relevanceScore += 10;
                            matchedFields.push({ field, type: 'fuzzy' });
                        }
                    }
                });

                if (relevanceScore > 0) {
                    matches.push({
                        ...item,
                        _relevanceScore: relevanceScore,
                        _matchedFields: matchedFields,
                        _dataType: dataType
                    });
                }
            });

            // Sort by relevance
            matches.sort((a, b) => b._relevanceScore - a._relevanceScore);
            results[dataType] = matches;
        });

        // Generate suggestions
        const allMatches = Object.values(results).flat();
        allMatches.forEach(match => {
            match._matchedFields.forEach(fieldMatch => {
                const suggestion = match[fieldMatch.field];
                if (suggestion && !suggestions.includes(suggestion)) {
                    suggestions.push(suggestion);
                }
            });
        });

        return {
            results,
            suggestions: suggestions.slice(0, 10) // Top 10 suggestions
        };
    }

    // Fuzzy matching algorithm
    fuzzyMatch(str1, str2, threshold = 0.7) {
        const distance = this.levenshteinDistance(str1, str2);
        const maxLength = Math.max(str1.length, str2.length);
        const similarity = 1 - (distance / maxLength);
        return similarity >= threshold;
    }

    // Levenshtein distance calculation
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    // Search by date range
    searchByDateRange(dataType, dateField, startDate, endDate) {
        const data = dataManager.getAll(dataType);
        const start = new Date(startDate);
        const end = new Date(endDate);

        return data.filter(item => {
            if (!item[dateField]) return false;
            const itemDate = new Date(item[dateField]);
            return itemDate >= start && itemDate <= end;
        });
    }

    // Search by numeric range
    searchByNumericRange(dataType, numericField, min, max) {
        const data = dataManager.getAll(dataType);

        return data.filter(item => {
            if (!item[numericField]) return false;
            const value = parseFloat(item[numericField]);
            return value >= min && value <= max;
        });
    }

    // Search with filters
    searchWithFilters(query, filters = {}) {
        let results = this.smartSearch(query).results;

        // Apply filters
        Object.keys(filters).forEach(filterType => {
            const filterValue = filters[filterType];
            
            Object.keys(results).forEach(dataType => {
                results[dataType] = results[dataType].filter(item => {
                    switch (filterType) {
                        case 'rank':
                            return !filterValue || item.rank === filterValue;
                        case 'battalion':
                            return !filterValue || item.battalion === filterValue;
                        case 'workplace':
                            return !filterValue || item.workplace === filterValue;
                        case 'dateFrom':
                            if (!filterValue) return true;
                            const dateFields = ['startDate', 'endDate', 'birthDate', 'appointmentDate'];
                            return dateFields.some(field => {
                                if (item[field]) {
                                    return new Date(item[field]) >= new Date(filterValue);
                                }
                                return false;
                            });
                        case 'dateTo':
                            if (!filterValue) return true;
                            const dateFields2 = ['startDate', 'endDate', 'birthDate', 'appointmentDate'];
                            return dateFields2.some(field => {
                                if (item[field]) {
                                    return new Date(item[field]) <= new Date(filterValue);
                                }
                                return false;
                            });
                        default:
                            return true;
                    }
                });
            });
        });

        return results;
    }

    // Add to search history
    addToSearchHistory(searchCriteria) {
        const historyItem = {
            criteria: searchCriteria,
            timestamp: new Date().toISOString(),
            id: Date.now().toString()
        };

        this.searchHistory.unshift(historyItem);

        // Keep only max items
        if (this.searchHistory.length > this.maxHistoryItems) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
        }

        // Save to localStorage
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    // Get search history
    getSearchHistory() {
        const saved = localStorage.getItem('searchHistory');
        if (saved) {
            this.searchHistory = JSON.parse(saved);
        }
        return this.searchHistory;
    }

    // Clear search history
    clearSearchHistory() {
        this.searchHistory = [];
        localStorage.removeItem('searchHistory');
    }

    // Get search suggestions based on history
    getSearchSuggestions(partialQuery) {
        const suggestions = new Set();
        
        this.searchHistory.forEach(historyItem => {
            Object.values(historyItem.criteria).forEach(criteria => {
                Object.values(criteria).forEach(value => {
                    if (typeof value === 'string' && value.toLowerCase().includes(partialQuery.toLowerCase())) {
                        suggestions.add(value);
                    }
                });
            });
        });

        return Array.from(suggestions).slice(0, 5);
    }

    // Export search results
    exportSearchResults(results, format = 'csv') {
        const allResults = Object.values(results).flat();
        
        if (allResults.length === 0) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        if (format === 'csv') {
            const headers = Object.keys(allResults[0]).filter(key => !key.startsWith('_'));
            const csvContent = [
                headers.join(','),
                ...allResults.map(row => 
                    headers.map(header => `"${row[header] || ''}"`).join(',')
                )
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `search_results_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // Get search analytics
    getSearchAnalytics() {
        const history = this.getSearchHistory();
        const analytics = {
            totalSearches: history.length,
            mostSearchedTerms: {},
            searchTrends: {},
            averageResultsPerSearch: 0
        };

        history.forEach(search => {
            const date = search.timestamp.split('T')[0];
            analytics.searchTrends[date] = (analytics.searchTrends[date] || 0) + 1;
        });

        return analytics;
    }
}

// Initialize advanced search engine
const advancedSearchEngine = new AdvancedSearchEngine();

// Export for global use
window.advancedSearchEngine = advancedSearchEngine;
