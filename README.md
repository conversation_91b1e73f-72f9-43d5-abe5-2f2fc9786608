# نظام إدارة بيانات الموظفين

نظام شامل لإدارة بيانات الموظفين مصمم بتقنيات حديثة وواجهة مستخدم عصرية بأسلوب Neumorphic.

## المميزات الرئيسية

### 🔐 نظام دخول آمن
- اسم المستخدم: `ISMAIL`
- كلمة المرور: `2026`

### 📊 الواجهات المتاحة

#### 1. بيانات الموظفين
- إدارة البيانات الشخصية والوظيفية
- تحميل صور الموظفين
- حساب السن والأقدمية تلقائياً
- البحث والتعديل والحذف

#### 2. العطل المرضية
- تسجيل أنواع مختلفة من العطل المرضية
- حساب الأيام المتبقية تلقائياً
- متابعة يومية للعطل النشطة

#### 3. العطل السنوية والاستثنائية
- إدارة العطل حسب السنة
- تتبع الأيام المتبقية
- تصنيف العطل (سنوية/استثنائية)

#### 4. العقوبات
- تسجيل العقوبات التأديبية
- تصنيف حسب الدرجة (1-4)
- متابعة المقررات والتبليغات

#### 5. الغيابات
- تسجيل أسباب الغياب
- تصنيف للحسم أو الحفظ
- إحصائيات شاملة

#### 6. الدورات التكوينية
- أنواع مختلفة من الدورات
- تحديد المستوى (محلي/جهوي/مركزي)
- متابعة مواعيد الدورات

#### 7. الإجازات
- حساب الأيام المتبقية تلقائياً
- متابعة يومية للإجازات النشطة

#### 8. التنقيط السنوي
- تقييم الأداء (4-7 نقاط)
- توليد تعليقات تلقائية
- تقارير سنوية شاملة

#### 9. الإحصائيات
- إحصائيات شاملة لجميع البيانات
- تقارير حسب الفترة الزمنية
- رسوم بيانية تفاعلية

#### 10. عرض ملفات CSV
- عرض جميع البيانات المحفوظة
- إمكانية التصدير والاستيراد

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التنسيق بأسلوب Neumorphic
- **JavaScript ES6+**: الوظائف والتفاعل
- **Bootstrap 5**: الإطار الأساسي
- **Bootstrap Icons**: الأيقونات
- **localStorage**: حفظ البيانات محلياً

## المميزات التقنية

### 🎨 تصميم Neumorphic
- تدرجات لونية دقيقة
- ظلال ثلاثية الأبعاد
- واجهة مستخدم عصرية

### 💾 حفظ البيانات
- استخدام localStorage للعمل بدون إنترنت
- نسخ احتياطي تلقائي
- استيراد وتصدير CSV

### 📱 تصميم متجاوب
- يعمل على جميع الأجهزة
- واجهة محسنة للهواتف المحمولة

### 🔍 البحث والتصفية
- بحث سريع برقم الذاتية
- تصفية متقدمة للبيانات

### 📈 الإحصائيات المتقدمة
- تقارير شاملة
- إحصائيات حسب الفترة
- تصدير التقارير

## كيفية الاستخدام

1. **الدخول للنظام**
   - افتح ملف `index.html`
   - أدخل اسم المستخدم: `ISMAIL`
   - أدخل كلمة المرور: `2026`

2. **إضافة بيانات جديدة**
   - اضغط على الواجهة المطلوبة
   - املأ النموذج
   - اضغط "حفظ"

3. **البحث والتعديل**
   - استخدم مربع البحث
   - اضغط على زر التعديل
   - احفظ التغييرات

4. **التصدير والاستيراد**
   - اضغط "تصدير CSV" لحفظ البيانات
   - استخدم "استيراد CSV" لاستعادة البيانات

## الملفات الرئيسية

- `index.html` - صفحة الدخول
- `main.html` - الواجهة الرئيسية
- `styles.css` - التنسيقات
- `script.js` - الوظائف الرئيسية
- `data-manager.js` - إدارة البيانات
- `statistics.js` - الإحصائيات

## المتطلبات

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب اتصال بالإنترنت للعمل

## الدعم والصيانة

النظام مصمم ليعمل بشكل مستقل ولا يتطلب صيانة خاصة. جميع البيانات محفوظة محلياً في المتصفح.

## الأمان

- البيانات محفوظة محلياً فقط
- لا يتم إرسال أي بيانات عبر الإنترنت
- نظام دخول بسيط لحماية الوصول

---

**تم التطوير بواسطة**: نظام إدارة بيانات الموظفين  
**الإصدار**: 1.0  
**التاريخ**: 2024
