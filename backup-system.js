// Advanced Backup and Recovery System
class BackupSystem {
    constructor() {
        this.backupInterval = 24 * 60 * 60 * 1000; // 24 hours
        this.maxBackups = 30; // Keep 30 backups
        this.compressionEnabled = true;
        this.encryptionEnabled = false;
        this.init();
    }

    // Initialize backup system
    init() {
        this.scheduleAutoBackup();
        this.cleanOldBackups();
        this.checkDataIntegrity();
    }

    // Schedule automatic backup
    scheduleAutoBackup() {
        const lastBackup = localStorage.getItem('lastBackupTime');
        const now = Date.now();
        
        if (!lastBackup || (now - parseInt(lastBackup)) > this.backupInterval) {
            this.createAutoBackup();
        }

        // Schedule next backup
        setInterval(() => {
            this.createAutoBackup();
        }, this.backupInterval);
    }

    // Create automatic backup
    createAutoBackup() {
        try {
            const backupData = this.gatherAllData();
            const backupId = this.generateBackupId();
            
            // Compress if enabled
            const finalData = this.compressionEnabled ? 
                this.compressData(backupData) : backupData;

            // Store backup
            localStorage.setItem(`backup_${backupId}`, JSON.stringify(finalData));
            localStorage.setItem('lastBackupTime', Date.now().toString());
            
            // Update backup list
            this.updateBackupList(backupId);
            
            console.log(`Auto backup created: ${backupId}`);
            this.showNotification('تم إنشاء نسخة احتياطية تلقائية', 'success');
        } catch (error) {
            console.error('Auto backup failed:', error);
            this.showNotification('فشل في إنشاء النسخة الاحتياطية التلقائية', 'error');
        }
    }

    // Create manual backup
    createManualBackup(description = '') {
        try {
            const backupData = this.gatherAllData();
            const backupId = this.generateBackupId();
            
            // Add metadata
            backupData.metadata = {
                type: 'manual',
                description: description,
                timestamp: new Date().toISOString(),
                version: '1.0',
                dataIntegrity: this.calculateChecksum(backupData)
            };

            const finalData = this.compressionEnabled ? 
                this.compressData(backupData) : backupData;

            localStorage.setItem(`backup_${backupId}`, JSON.stringify(finalData));
            this.updateBackupList(backupId);
            
            return backupId;
        } catch (error) {
            throw new Error('فشل في إنشاء النسخة الاحتياطية: ' + error.message);
        }
    }

    // Gather all data
    gatherAllData() {
        const dataTypes = [
            'employees', 'sickLeave', 'annualLeave', 'penalties', 
            'absences', 'training', 'vacations', 'evaluations'
        ];

        const allData = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            totalRecords: 0
        };

        dataTypes.forEach(type => {
            const data = dataManager.getAll(type);
            allData[type] = data;
            allData.totalRecords += data.length;
        });

        // Add system settings
        allData.settings = {
            lastLogin: localStorage.getItem('lastLogin'),
            userPreferences: localStorage.getItem('userPreferences'),
            systemConfig: localStorage.getItem('systemConfig')
        };

        return allData;
    }

    // Generate backup ID
    generateBackupId() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '');
        const random = Math.random().toString(36).substr(2, 4);
        return `${dateStr}_${timeStr}_${random}`;
    }

    // Update backup list
    updateBackupList(backupId) {
        let backupList = JSON.parse(localStorage.getItem('backupList') || '[]');
        
        backupList.push({
            id: backupId,
            timestamp: new Date().toISOString(),
            size: this.calculateBackupSize(backupId)
        });

        // Sort by timestamp (newest first)
        backupList.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // Keep only max backups
        if (backupList.length > this.maxBackups) {
            const oldBackups = backupList.slice(this.maxBackups);
            oldBackups.forEach(backup => {
                localStorage.removeItem(`backup_${backup.id}`);
            });
            backupList = backupList.slice(0, this.maxBackups);
        }

        localStorage.setItem('backupList', JSON.stringify(backupList));
    }

    // Calculate backup size
    calculateBackupSize(backupId) {
        const backupData = localStorage.getItem(`backup_${backupId}`);
        return backupData ? backupData.length : 0;
    }

    // Clean old backups
    cleanOldBackups() {
        const backupList = JSON.parse(localStorage.getItem('backupList') || '[]');
        const cutoffDate = new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)); // 30 days

        const validBackups = backupList.filter(backup => {
            const backupDate = new Date(backup.timestamp);
            if (backupDate < cutoffDate) {
                localStorage.removeItem(`backup_${backup.id}`);
                return false;
            }
            return true;
        });

        localStorage.setItem('backupList', JSON.stringify(validBackups));
    }

    // Restore from backup
    restoreFromBackup(backupId) {
        try {
            const backupData = localStorage.getItem(`backup_${backupId}`);
            if (!backupData) {
                throw new Error('النسخة الاحتياطية غير موجودة');
            }

            let data = JSON.parse(backupData);
            
            // Decompress if needed
            if (this.compressionEnabled && data.compressed) {
                data = this.decompressData(data);
            }

            // Verify data integrity
            if (!this.verifyDataIntegrity(data)) {
                throw new Error('النسخة الاحتياطية تالفة');
            }

            // Create current backup before restore
            const currentBackupId = this.createManualBackup('قبل الاستعادة');

            // Restore data
            const dataTypes = [
                'employees', 'sickLeave', 'annualLeave', 'penalties', 
                'absences', 'training', 'vacations', 'evaluations'
            ];

            dataTypes.forEach(type => {
                if (data[type]) {
                    localStorage.setItem(type, JSON.stringify(data[type]));
                }
            });

            // Restore settings
            if (data.settings) {
                Object.keys(data.settings).forEach(key => {
                    if (data.settings[key]) {
                        localStorage.setItem(key, data.settings[key]);
                    }
                });
            }

            this.showNotification('تم استعادة البيانات بنجاح', 'success');
            return true;
        } catch (error) {
            this.showNotification('فشل في استعادة البيانات: ' + error.message, 'error');
            return false;
        }
    }

    // Get backup list
    getBackupList() {
        return JSON.parse(localStorage.getItem('backupList') || '[]');
    }

    // Delete backup
    deleteBackup(backupId) {
        localStorage.removeItem(`backup_${backupId}`);
        
        let backupList = this.getBackupList();
        backupList = backupList.filter(backup => backup.id !== backupId);
        localStorage.setItem('backupList', JSON.stringify(backupList));
    }

    // Export backup to file
    exportBackup(backupId) {
        const backupData = localStorage.getItem(`backup_${backupId}`);
        if (!backupData) {
            throw new Error('النسخة الاحتياطية غير موجودة');
        }

        const blob = new Blob([backupData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `backup_${backupId}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }

    // Import backup from file
    importBackup(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    const backupId = this.generateBackupId();
                    
                    localStorage.setItem(`backup_${backupId}`, JSON.stringify(data));
                    this.updateBackupList(backupId);
                    
                    resolve(backupId);
                } catch (error) {
                    reject(new Error('ملف النسخة الاحتياطية تالف'));
                }
            };
            reader.readAsText(file);
        });
    }

    // Compress data (simple compression)
    compressData(data) {
        // Simple compression by removing whitespace
        const compressed = JSON.stringify(data);
        return {
            compressed: true,
            data: compressed,
            originalSize: JSON.stringify(data, null, 2).length,
            compressedSize: compressed.length
        };
    }

    // Decompress data
    decompressData(compressedData) {
        if (compressedData.compressed) {
            return JSON.parse(compressedData.data);
        }
        return compressedData;
    }

    // Calculate checksum for data integrity
    calculateChecksum(data) {
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Verify data integrity
    verifyDataIntegrity(data) {
        if (!data.metadata || !data.metadata.dataIntegrity) {
            return true; // Skip verification for old backups
        }

        const currentChecksum = this.calculateChecksum(data);
        return currentChecksum === data.metadata.dataIntegrity;
    }

    // Check data integrity
    checkDataIntegrity() {
        const dataTypes = [
            'employees', 'sickLeave', 'annualLeave', 'penalties', 
            'absences', 'training', 'vacations', 'evaluations'
        ];

        let corruptedData = [];

        dataTypes.forEach(type => {
            try {
                const data = JSON.parse(localStorage.getItem(type) || '[]');
                if (!Array.isArray(data)) {
                    corruptedData.push(type);
                }
            } catch (error) {
                corruptedData.push(type);
            }
        });

        if (corruptedData.length > 0) {
            this.showNotification(
                `تم اكتشاف بيانات تالفة في: ${corruptedData.join(', ')}`, 
                'warning'
            );
        }

        return corruptedData.length === 0;
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease;
        `;
        notification.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${message}</span>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Get backup statistics
    getBackupStatistics() {
        const backupList = this.getBackupList();
        const totalSize = backupList.reduce((sum, backup) => sum + backup.size, 0);
        
        return {
            totalBackups: backupList.length,
            totalSize: totalSize,
            oldestBackup: backupList.length > 0 ? backupList[backupList.length - 1].timestamp : null,
            newestBackup: backupList.length > 0 ? backupList[0].timestamp : null,
            lastAutoBackup: localStorage.getItem('lastBackupTime')
        };
    }
}

// Initialize backup system
const backupSystem = new BackupSystem();

// Export for global use
window.backupSystem = backupSystem;
