<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة بيانات الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="bi bi-shield-check login-icon"></i>
                <h2>نظام إدارة بيانات الموظفين</h2>
                <p>مرحباً بك في نظام إدارة شؤون الموظفين</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-container">
                        <i class="bi bi-person-fill"></i>
                        <input type="text" id="username" name="username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-container">
                        <i class="bi bi-lock-fill"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="bi bi-eye-fill"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="bi bi-box-arrow-in-right"></i>
                    دخول
                </button>
                
                <div id="error-message" class="error-message" style="display: none;">
                    اسم المستخدم أو كلمة المرور غير صحيحة
                </div>
            </form>
            
            <div class="login-footer">
                <p>© 2024 نظام إدارة بيانات الموظفين</p>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'bi bi-eye-slash-fill';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'bi bi-eye-fill';
            }
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('error-message');
            
            if (username === 'ISMAIL' && password === '2026') {
                localStorage.setItem('isLoggedIn', 'true');
                window.location.href = 'main.html';
            } else {
                errorMessage.style.display = 'block';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            }
        });

        // Check if already logged in
        if (localStorage.getItem('isLoggedIn') === 'true') {
            window.location.href = 'main.html';
        }
    </script>
</body>
</html>
