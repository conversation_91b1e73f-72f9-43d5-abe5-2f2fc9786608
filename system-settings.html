<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة بيانات الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .settings-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-info h6 {
            margin: 0;
            color: var(--dark-color);
            font-weight: 600;
        }
        
        .setting-info small {
            color: #666;
        }
        
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--primary-color);
        }
        
        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active::before {
            transform: translateX(30px);
        }
        
        .backup-item {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 3px solid var(--secondary-color);
        }
        
        .backup-item:hover {
            background: rgba(108, 92, 231, 0.1);
        }
        
        .system-info-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .info-metric {
            margin-bottom: 10px;
        }
        
        .info-metric h4 {
            margin: 0;
            font-weight: 700;
        }
        
        .info-metric small {
            opacity: 0.9;
        }
    </style>
</head>
<body class="main-body">
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="bi bi-gear-fill"></i>
                <h1>إعدادات النظام</h1>
            </div>
            
            <div class="user-info">
                <a href="main.html" class="btn btn-primary">
                    <i class="bi bi-house-fill"></i>
                    الرئيسية
                </a>
                <a href="dashboard.html" class="btn btn-info">
                    <i class="bi bi-speedometer2"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <main class="dashboard">
        <div class="row">
            <!-- System Information -->
            <div class="col-md-4">
                <div class="system-info-card">
                    <h5><i class="bi bi-info-circle"></i> معلومات النظام</h5>
                    <div class="info-metric">
                        <h4 id="totalRecords">0</h4>
                        <small>إجمالي السجلات</small>
                    </div>
                    <div class="info-metric">
                        <h4 id="storageUsed">0 KB</h4>
                        <small>مساحة التخزين المستخدمة</small>
                    </div>
                    <div class="info-metric">
                        <h4 id="lastBackup">لا يوجد</h4>
                        <small>آخر نسخة احتياطية</small>
                    </div>
                    <div class="info-metric">
                        <h4 id="systemUptime">0 دقيقة</h4>
                        <small>مدة تشغيل الجلسة</small>
                    </div>
                </div>
            </div>
            
            <!-- Settings Sections -->
            <div class="col-md-8">
                <!-- Notification Settings -->
                <div class="settings-section">
                    <h5><i class="bi bi-bell-fill"></i> إعدادات التنبيهات</h5>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>تفعيل التنبيهات</h6>
                            <small>تشغيل/إيقاف نظام التنبيهات الذكية</small>
                        </div>
                        <div class="toggle-switch" id="notificationsEnabled" onclick="toggleSetting('notificationsEnabled')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>الأصوات</h6>
                            <small>تشغيل الأصوات عند وصول التنبيهات</small>
                        </div>
                        <div class="toggle-switch" id="soundEnabled" onclick="toggleSetting('soundEnabled')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>تنبيهات سطح المكتب</h6>
                            <small>عرض التنبيهات على سطح المكتب</small>
                        </div>
                        <div class="toggle-switch" id="desktopEnabled" onclick="toggleSetting('desktopEnabled')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>فترة التحقق (بالدقائق)</h6>
                            <small>كم مرة يتم التحقق من التنبيهات الجديدة</small>
                        </div>
                        <select class="form-control" id="checkInterval" onchange="updateCheckInterval()" style="width: 120px;">
                            <option value="30000">0.5</option>
                            <option value="60000">1</option>
                            <option value="300000">5</option>
                            <option value="600000">10</option>
                            <option value="1800000">30</option>
                        </select>
                    </div>
                </div>

                <!-- Backup Settings -->
                <div class="settings-section">
                    <h5><i class="bi bi-cloud-arrow-down-fill"></i> إعدادات النسخ الاحتياطي</h5>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>النسخ الاحتياطي التلقائي</h6>
                            <small>إنشاء نسخة احتياطية تلقائياً كل 24 ساعة</small>
                        </div>
                        <div class="toggle-switch active" id="autoBackup" onclick="toggleSetting('autoBackup')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>ضغط البيانات</h6>
                            <small>ضغط النسخ الاحتياطية لتوفير المساحة</small>
                        </div>
                        <div class="toggle-switch active" id="compressionEnabled" onclick="toggleSetting('compressionEnabled')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>عدد النسخ المحفوظة</h6>
                            <small>الحد الأقصى لعدد النسخ الاحتياطية المحفوظة</small>
                        </div>
                        <select class="form-control" id="maxBackups" onchange="updateMaxBackups()" style="width: 120px;">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="createManualBackup()">
                            <i class="bi bi-cloud-download"></i>
                            إنشاء نسخة احتياطية الآن
                        </button>
                        <button class="btn btn-success" onclick="showBackupManager()">
                            <i class="bi bi-list"></i>
                            إدارة النسخ الاحتياطية
                        </button>
                    </div>
                </div>

                <!-- Data Management -->
                <div class="settings-section">
                    <h5><i class="bi bi-database-fill"></i> إدارة البيانات</h5>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>تنظيف البيانات التلقائي</h6>
                            <small>حذف البيانات القديمة والمكررة تلقائياً</small>
                        </div>
                        <div class="toggle-switch" id="autoCleanup" onclick="toggleSetting('autoCleanup')"></div>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-warning" onclick="optimizeDatabase()">
                            <i class="bi bi-speedometer"></i>
                            تحسين قاعدة البيانات
                        </button>
                        <button class="btn btn-info" onclick="exportAllData()">
                            <i class="bi bi-download"></i>
                            تصدير جميع البيانات
                        </button>
                        <button class="btn btn-danger" onclick="resetSystem()">
                            <i class="bi bi-arrow-clockwise"></i>
                            إعادة تعيين النظام
                        </button>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="settings-section">
                    <h5><i class="bi bi-shield-fill"></i> إعدادات الأمان</h5>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>تسجيل العمليات</h6>
                            <small>تسجيل جميع العمليات المنجزة في النظام</small>
                        </div>
                        <div class="toggle-switch active" id="auditLog" onclick="toggleSetting('auditLog')"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h6>انتهاء الجلسة التلقائي</h6>
                            <small>تسجيل الخروج التلقائي بعد فترة عدم النشاط</small>
                        </div>
                        <select class="form-control" id="sessionTimeout" onchange="updateSessionTimeout()" style="width: 120px;">
                            <option value="0">معطل</option>
                            <option value="1800000">30 دقيقة</option>
                            <option value="3600000">ساعة</option>
                            <option value="7200000">ساعتان</option>
                            <option value="14400000">4 ساعات</option>
                        </select>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-info" onclick="viewAuditLog()">
                            <i class="bi bi-list-ul"></i>
                            عرض سجل العمليات
                        </button>
                        <button class="btn btn-warning" onclick="changePassword()">
                            <i class="bi bi-key"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Manager Modal -->
        <div class="modal fade" id="backupManagerModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إدارة النسخ الاحتياطية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="backupList">
                            <!-- Backup list will be populated here -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-danger" onclick="cleanOldBackups()">
                            <i class="bi bi-trash"></i>
                            حذف النسخ القديمة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="data-manager.js"></script>
    <script src="backup-system.js"></script>
    <script src="notification-system.js"></script>
    <script src="system-settings.js"></script>

    <script>
        // Check authentication
        if (localStorage.getItem('isLoggedIn') !== 'true') {
            window.location.href = 'index.html';
        }

        // Initialize settings page
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettings();
        });
    </script>
</body>
</html>
